const buildMode = process.env.BUILD_MODE;
const isStaging = buildMode === 'staging';
const isProduction = buildMode === 'prod';

export default {
  production: isProduction || isStaging,
  apiUrl: '__API_URL__',
  azureB2CTenantName: '__B2C_TENANT__',
  azureB2CClientId: '__B2C_CLIENTID__',
  azureB2CPolicy: '__B2C_POLICY_NEW__',
  azureResetPWPolicy: '__B2C_RESET_PW_POLICY_NEW__',
  azureRedirectUri: '__B2C_REDIRECT_URI__',
  inactivityTime: '__INACTIVITY_TIME__',
};
