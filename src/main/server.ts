import * as hapi from '@hapi/hapi';

import * as AzAppInsights from './_common/azure/AzAppInsights';
import AzRedisCache from './_common/azure/AzRedisCache';
import logger from './_common/utils/LogUtils';

import { LmdBoxCPlugin } from './lmdBoxC';
import { LmdCPLPlugin } from './lmdCouriersPlease';
import { LmdEvriPlugin } from './lmdEvri/LmdEvriPlugin';
import { LmdHjPlugin } from './lmdHanjin';
import { LmdJanioPlugin } from './lmdJanio';
import { LmdKerryPlugin } from './lmdKerry';
import { LmdKerryHKPlugin } from './lmdKerryHK';
import { LmdLMGPlugin } from './lmdLMG';
import { LmdNZPostPlugin } from './lmdNewZealandPost';
import { LmdNinjavanPlugin } from './lmdNinjavan';
import { LmdPickuppPlugin } from './lmdPickupp/LmdPickuppPlugin';
import { LmdSagawaPlugin } from './lmdSagawa';
import { LmdWMGPlugin } from './lmdWMG';

import { AirportPlugin } from './airportService';
import { AusPostOrderPlugin } from './ausPost/AusPostOrderPlugin';
import { AusPostStatusPlugin } from './ausPost/AusPostStatusPlugin';
import { CachePlugin } from './cache';
import { CancelBookingPlugin } from './cancelBooking';
import { AMSStatusPlugin } from './cbAMS';
import { CbStatusTriggerPlugin } from './cbStatusTrigger';
import { CollectionPointPlugin } from './collectionPoint';
import { CounterPlugin } from './counter';
import { CustomerNotificationPlugin } from './customerNotification/CustomerNotificationPlugin';
import { DestinationPlugin } from './destinations';
import { EnumMapperPlugin } from './enumMapper';
import { ExchangeRatePlugin } from './exchangeRate';
import { FirstMileProviderPlugin } from './firstMileProvider';
import { HSCodePlugin } from './hsCode';
import { JobInformCbPlugin } from './jobInformCb';
import { loadAppProperties } from './loadAppProperties';
import { MawbPlugin } from './mawb';
import { MerchantPlugin } from './merchant/MerchantPlugin';
import { MerchantWebHookService } from './merchantWebHook';
import { MockLMDPlugin } from './mockLMD';
import { OpsHubPlugin } from './operationHub';
import { ParcelExpiredPlugin } from './parcelExpired';
import { RateZonePlugin } from './rateZone';
import { AppConfig } from './resources';
import { SystemConfigurationsPlugin } from './systemConfiguration';
import { TaxPlugin } from './tax';
import { TaxCodePlugin } from './taxCode';
import { TimezonePlugin } from './timezone';

AzAppInsights.init();

const port = process.env.PORT || '3009';

const server: hapi.Server = new hapi.Server({
  port,
  routes: {
    cors: true,
  },
  load: {
    sampleInterval: 10,
    concurrent: 10,
  },
});

const onRequest = function (request, hapiToolKit) {
  request.timeRequest = Date.now();

  return hapiToolKit.continue;
};

server.ext('onRequest', onRequest);

const preResponse = async function (request, hapiToolKit) {
  try {
    const { raw, timeRequest, headers, payload, response } = request;
    const { method, url } = raw.req;

    if (method === 'OPTIONS' || url === '/') {
      return hapiToolKit.continue;
    }

    let payloadString = '';

    if (
      ['POST', 'PATCH', 'PUT'].includes(method) &&
      headers['content-type']?.includes('application/json')
    ) {
      payloadString = `payload=${JSON.stringify(payload)}`;
    }

    const { statusCode, source } = response;
    logger.info(
      'preResponse',
      [
        'Received request',
        `request="${method} ${url}"`,
        `responseStatus=${statusCode}`,
        `responseTime=${Date.now() - timeRequest}`,
        statusCode >= 400 || source?.success === false ? `error=${JSON.stringify(source)}` : '',
        payloadString,
      ]
        .filter(Boolean)
        .join(', '),
    );
  } catch (error) {
    logger.error('preResponse', error);
  }

  return hapiToolKit.continue;
};

server.ext('onPreResponse', preResponse);

server.route({
  method: 'GET',
  path: '/',
  handler: () => {
    return 'PLS Financial API';
  },
});
const start = async () => {
  try {
    await loadAppProperties();

    MerchantWebHookService.init();
    AzRedisCache.initialize(process.env.REDISCACHEHOSTNAME, AppConfig.redis.key);

    await server.register([
      new AirportPlugin(),
      new AusPostOrderPlugin(),
      new AusPostStatusPlugin(),
      new CounterPlugin(),
      new CustomerNotificationPlugin(),
      new DestinationPlugin(),
      new EnumMapperPlugin(),
      new ExchangeRatePlugin(),
      new FirstMileProviderPlugin(),
      new HSCodePlugin(),
      new JobInformCbPlugin(),
      new LmdBoxCPlugin(),
      new LmdHjPlugin(),
      new LmdKerryPlugin(),
      new LmdKerryHKPlugin(),
      new LmdSagawaPlugin(),
      new LmdWMGPlugin(),
      new LmdLMGPlugin(),
      new LmdEvriPlugin(),
      new LmdNinjavanPlugin(),
      new MawbPlugin(),
      new MockLMDPlugin(),
      new OpsHubPlugin(),
      new RateZonePlugin(),
      new TaxCodePlugin(),
      new TaxPlugin(),
      new TimezonePlugin(),
      new SystemConfigurationsPlugin(),
      new LmdJanioPlugin(),
      new CollectionPointPlugin(),
      new LmdCPLPlugin(),
      new AMSStatusPlugin(),
      new LmdNZPostPlugin(),
      new CancelBookingPlugin(),
      new LmdPickuppPlugin(),
      new ParcelExpiredPlugin(),
      new CbStatusTriggerPlugin(),
      new MerchantPlugin(),
      new CachePlugin(),
    ]);
    await server.start();
  } catch (error) {
    logger.error('PLS-FINANCIAL-API-START', error);
    process.exit(1);
  }

  logger.info('PLS-FINANCIAL-API-START', 'Server running at:', server.info.uri);
};

start().catch((error) => {
  logger.error('PLS-FINANCIAL-API-START', error);
});
