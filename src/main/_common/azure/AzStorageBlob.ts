import path from 'path';

import { ManagedIdentityCredential } from '@azure/identity';
import { BlobItem, BlobServiceClient } from '@azure/storage-blob';

import { format } from 'date-fns';

import { EXT } from '../Enum';
import logger from '../utils/LogUtils';

import AzKeyVault from './AzKeyVault';

export enum AzStorageContainer {
  AIRPORT = 'airport',
  CONFIG = 'configuration',
  CPL_TRACKING_STATUS = 'cplbackuptrackingstatus',
  ENUM_MAPPER = 'enummapper',
  HS_CODE = 'hscode',
  MAWB = 'mawb',
  OVERPACK_LABEL = 'overpacklabel',
  LABEL = 'plsparcellabel',
  RATE_ZONE = 'ratezone',
  SAGAWA_TRACKING_STATUS = 'sagawabackuptrackingstatus',
  EXCHANGE_RATE = 'sapexchangerate',
  USER_CONFIG = 'userconfigurations',
  KOREA_TYPE_2_CLEARANCE = 'koreatype2clearance',
}

export default class AzStorageBlob {
  static blobServiceClient: BlobServiceClient;

  static async initServiceClient() {
    const internalStorageAccount = await AzKeyVault.getSecret('internal-storage-account');
    const managedIdentityClientId = await AzKeyVault.getSecret('managed-identity-client-id');
    this.blobServiceClient = new BlobServiceClient(
      `https://${internalStorageAccount}.blob.core.windows.net`,
      new ManagedIdentityCredential(managedIdentityClientId),
    );
  }

  public static uploadBlob(containerName: string, blobName: string, buffer: Buffer | string) {
    const containerClient = this.blobServiceClient.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    return blockBlobClient.upload(buffer, buffer.length);
  }

  public static downloadBlob(containerName: string, blobName: string): Promise<Buffer> {
    const containerClient = this.blobServiceClient.getContainerClient(containerName);
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    return blockBlobClient.downloadToBuffer();
  }

  public static isBlobExist(containerName: string, blobName: string): Promise<boolean> {
    const containerClient = this.blobServiceClient.getContainerClient(containerName);
    const blobClient = containerClient.getBlockBlobClient(blobName);

    return blobClient.exists();
  }

  public static async deleteBlob(containerName: string, blobName: string) {
    try {
      const containerClient = this.blobServiceClient.getContainerClient(containerName);
      const blobClient = containerClient.getBlockBlobClient(blobName);

      await blobClient.deleteIfExists();
    } catch (error) {
      logger.error('AzStorageBlob.deleteBlob', containerName, blobName, error);
    }
  }

  public static async listBlobs(containerName: string) {
    const result = [];

    const containerClient = this.blobServiceClient.getContainerClient(containerName);

    for await (const blob of containerClient.listBlobsFlat()) {
      result.push(blob.name);
    }

    return result;
  }

  /*
   * Example:
   * input: 'PXL001.pdf'
   * output: 'PXL001/PXL001-20250516102035.pdf'
   */
  static generateBlobNameWithTimestamp(blobName: string): string {
    const { dir, ext, name } = path.parse(blobName);

    const timestamp = format(new Date(), 'yyyyMMddHHmmss');

    return `${dir ? `${dir}/` : ''}${name}/${name}-${timestamp}${ext.toLowerCase()}`;
  }

  /*
   * NOTE:
   * blobNamePrefix can be with or without "/". If there are multiple types of blob which have the same prefix name, we should append "/" so we can search by directory and avoid to list the wrong files.
   */
  static async getLatestFileByTimestamp(
    blobNamePrefix: string,
    blobExtension: EXT,
    blobContainer: AzStorageContainer,
  ): Promise<BlobItem | undefined> {
    const containerClient = this.blobServiceClient.getContainerClient(blobContainer);

    const blobs = containerClient.listBlobsFlat({
      prefix: blobNamePrefix,
      includeTags: false,
      includeCopy: false,
      includeDeleted: false,
      includeDeletedWithVersions: false,
      includeLegalHold: false,
      includeSnapshots: false,
      includeUncommitedBlobs: false,
      includeImmutabilityPolicy: false,
      includeMetadata: false,
      includeVersions: false,
    });

    let latestBlob: BlobItem | undefined;
    let latestTimestamp = new Date(0);

    for await (const blob of blobs) {
      if (
        blob.properties?.createdOn > latestTimestamp &&
        blob.name.toLowerCase().endsWith(blobExtension.toLowerCase())
      ) {
        latestTimestamp = blob.properties?.createdOn;
        latestBlob = blob;
      }
    }

    return latestBlob;
  }
}
