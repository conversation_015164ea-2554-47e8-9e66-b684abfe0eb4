import { ManagedIdentityCredential } from '@azure/identity';
import {
  QueueDeleteMessageResponse,
  DequeuedMessageItem,
  QueueSendMessageResponse,
  QueueServiceClient,
} from '@azure/storage-queue';

import logger, { FunctionName } from '../utils/LogUtils';

import AzKeyVault from './AzKeyVault';

export const AzStorageQueueName = {
  MERCHANT_WEBHOOK_QUEUE_NAME: 'merchant-status-webhook',
  PARCELS_UPDATE_PARENT_SPLIT_STATUS: 'parcels-update-parent-split-status',
};

export class AzStorageQueue {
  static queueServiceClient: QueueServiceClient;

  static async initServiceClient() {
    const internalStorageAccount = await AzKeyVault.getSecret('internal-storage-account');
    const managedIdentityClientId = await AzKeyVault.getSecret('managed-identity-client-id');
    this.queueServiceClient = new QueueServiceClient(
      `https://${internalStorageAccount}.queue.core.windows.net`,
      new ManagedIdentityCredential(managedIdentityClientId),
    );
  }

  public static sendMessage(
    queueName: string,
    message: string,
    expiredIn = -1, // -1 means never expire
  ): Promise<QueueSendMessageResponse> {
    const queueServiceClient = this.queueServiceClient.getQueueClient(queueName);

    return queueServiceClient.sendMessage(message, { messageTimeToLive: expiredIn });
  }

  public static sendBase64Message(
    queueName: string,
    message: any,
    expiredIn: number = 60 * 60 * 24 * 7, // 7 days in seconds
  ): Promise<QueueSendMessageResponse> {
    const jsonMessage = JSON.stringify(message);
    logger.info(
      FunctionName.AZ_STORAGE_QUEUE,
      `Start pushing to queue ${queueName} with message ${jsonMessage}`,
    );
    const base64Message = Buffer.from(jsonMessage).toString('base64');

    return this.sendMessage(queueName, base64Message, expiredIn);
  }

  public static async receiveMessages(
    queueName: string,
    numberOfMessages: number,
  ): Promise<DequeuedMessageItem[]> {
    const queueServiceClient = this.queueServiceClient.getQueueClient(queueName);

    return (await queueServiceClient.receiveMessages({ numberOfMessages })).receivedMessageItems;
  }

  public static deleteMessage(
    queueName: string,
    messageId: string,
    popReceipt: string,
  ): Promise<QueueDeleteMessageResponse> {
    const queueServiceClient = this.queueServiceClient.getQueueClient(queueName);

    return queueServiceClient.deleteMessage(messageId, popReceipt);
  }
}
