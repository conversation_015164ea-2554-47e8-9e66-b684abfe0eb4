import { promisify } from 'util';

import redis, { RedisClient } from 'redis';

import { ParcelStatus } from '../Constants';
import logger from '../utils/LogUtils';

export enum AzRedisKey {
  AIRPORT = 'Airports',
  AU_POST_DEFAULT_RETURN_ADDRESS = 'AuPostDefaultReturnAddress',
  BOXC_ERR_MAP = 'boxc-error-mapping',
  COUNTRY = 'countries',
  CUSTOM_BROKER = 'customBroker',
  DESTINATION_V2 = 'destinationV2',
  HS_CODE_BLACKLIST = 'hscodeBlacklist',
  HS_CODE_LIST_HK_CONSOLE_MANIFEST = 'hscode',
  HS_CODE_LIST_AU = 'hscodeAU',
  HS_CODE_LIST_NZ = 'hscodeNZ',
  HS_CODE_MASTER_LIST = 'hscodeMasterListMapping',
  KERRY_HK_TOKEN = 'KerryHKToken',
  LOCATION = 'location',
  NJV_TOKEN = 'NinjavanToken',
  OPERATION_HUB = 'opHub', // TODOS: why 2 operation hubs cache?
  RATE_ZONE_V2 = 'RateZone_v2',
  SYS_CONFIG = 'SystemConfigurations',
  PARCEL_STATUSES = 'parcelStatuses',
  COLLECTION_POINTS = 'collection_points',
  NJV_PUDO_POINTS_SG = 'NJV_PUDO_points_SG',
  NJV_PUDO_POINTS_MY = 'NJV_PUDO_points_MY',
  EXCHANGE_RATE = 'exchangeRate',
  TAX_CODE = 'TaxCode',
  TIMEZONE_PREFIX = 'timezone_',
  TIN_LIST = 'TaiwanImportNumbers',
  OP_HUBS = 'op_hubs',
  MERCHANT = 'MERCHANT',
  WMG_SG_TOKEN = 'WmgSGToken',
  WMG_GB_TOKEN = 'WmgGBToken',
  WMG_MY_TOKEN = 'WmgMYToken',
  WMG_KR_TOKEN = 'WmgKRToken',
  CAT_1_MERCHANT_LIST = 'cat_1_merchant_list',
  CONDITIONAL_BOOKING_RULES = 'conditional-booking-rules',
}

// TODOS: Move out, it's specific for Status Mapping
export type RedisParcelStatusFields = {
  sequence: number;
  blockchain: string;
  manifestStt: string;
  merchantReport: string;
  allowCancel: boolean;
  allowLabel: boolean;
  categoryStt: string;
  allowSchedule: boolean;
};

export type RedisParcelStatusMapping = Record<ParcelStatus, RedisParcelStatusFields>;

export default class AzRedisCache {
  static redisClient: RedisClient;

  static initialize(host: string, password: string, port = 6380) {
    this.redisClient = redis.createClient(port, host, {
      auth_pass: password,
      tls: { servername: host },
    });
    this.redisClient.on('error', (err) => logger.error('AzRedisCache', 'Client error', err));
  }

  static async getAllKeys(): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.redisClient.keys('*', (err, keys) => {
        if (err) {
          reject(err);
        } else {
          resolve(keys);
        }
      });
    });
  }

  static getEnvCacheName(key: string): string {
    return `[${process.env.APP_ENV}]_${key}`;
  }

  static async setCacheStr(key: string, value: string, ttlInHrs?: number): Promise<string> {
    const cacheName = this.getEnvCacheName(key);

    try {
      if (ttlInHrs) {
        const setAsync = promisify(this.redisClient.setex).bind(this.redisClient);
        await setAsync(cacheName, ttlInHrs * 3600, value);
      } else {
        const setAsync = promisify(this.redisClient.set).bind(this.redisClient);
        await setAsync(cacheName, value);
      }
    } catch (error) {
      logger.error('AzRedisCache', 'Set cache error', cacheName, error);
    }

    return value;
  }

  static async getCacheStr(key: string): Promise<string> {
    const cacheName = this.getEnvCacheName(key);

    try {
      const getAsync = promisify(this.redisClient.get).bind(this.redisClient);

      return await getAsync(cacheName);
    } catch (error) {
      logger.error('AzRedisCache', 'Get cache error', cacheName, error);
    }

    return null;
  }

  static async setCacheObj(key: string, value, ttlInHrs?: number) {
    await this.setCacheStr(key, JSON.stringify(value), ttlInHrs);

    return value;
  }

  static async getCacheObj(key: string) {
    try {
      const value = await this.getCacheStr(key);
      const data = JSON.parse(value);

      return data;
    } catch {
      return null;
    }
  }

  static deleteKey(key: string) {
    return new Promise((resolve, reject) => {
      this.redisClient.del(this.getEnvCacheName(key), (err, keys) => {
        if (err) {
          reject(err);
        } else {
          resolve(keys);
        }
      });
    });
  }
}
