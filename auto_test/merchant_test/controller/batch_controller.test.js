const BatchController = require('../../../merchant/controller/batch_controller');

const aesUtils = require('../../../merchant/models/aesUtils');
const ManifestItemService = require('../../../merchant/services/manifest-item-services');
const ShipmentService = require('../../../merchant/services/shipment-services');
const StatusMappingService = require('../../../merchant/services/statusMappingService');
const reportUtils = require('../../../merchant/report/report.utils');
const bookingService = require('../../../merchant/services/booking/booking-service');

jest.mock('axios');


const manifestItemDao = {};
const batchController = new BatchController(manifestItemDao);
ManifestItemService.manifestItemDao = manifestItemDao;

beforeEach(() => {
  jest.clearAllMocks();
  StatusMappingService.isMerchantVisible = jest.fn().mockReturnValue(true);
});

describe('getBatchByBatchNo', () => {
    const batchDate = '********';
    const shipmentType = 'domestic';
    const merchantAccount = '10536d';
    const mockBatchNo = `${batchDate}-${shipmentType}-x-${merchantAccount}`;
    const mockMerchantName = 'test_merchant';

    const mockReq = {
        params: {
            batchNo: mockBatchNo
        },
        merchant: {
            merchant_name: mockMerchantName
        }
    };
    const mockRes = {
        json: jest.fn(),
    };
    mockRes.status = jest.fn().mockReturnValue(mockRes);

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return 500 if error occur', async () => {
        // Arrange

        // Action
        await batchController.getBatchByBatchNo(null, mockRes);

        // Assert
        expect(mockRes.status).toHaveBeenCalledWith(500);
        expect(mockRes.json).toHaveBeenCalledTimes(1);
    });

    it('should return batch info when merchant owns the batch', async () => {
        // Arrange
        const req = {...mockReq};
        ManifestItemService.validateBatchOwnership = jest.fn().mockResolvedValue(true);

        // Action
        await batchController.getBatchByBatchNo(req, mockRes);

        // Assert
        expect(ManifestItemService.validateBatchOwnership).toHaveBeenCalledWith(mockMerchantName, mockBatchNo);
        expect(mockRes.json).toHaveBeenCalledWith({
            data: {
                new_batch_no: '000230530d',
                date: batchDate
            },
            isSuccess: true
        });
    });

    it('should return 404 when merchant does not own the batch', async () => {
        // Arrange
        const req = {...mockReq};
        ManifestItemService.validateBatchOwnership = jest.fn().mockResolvedValue(false);

        // Action
        await batchController.getBatchByBatchNo(req, mockRes);

        // Assert
        expect(ManifestItemService.validateBatchOwnership).toHaveBeenCalledWith(mockMerchantName, mockBatchNo);
        expect(mockRes.status).toHaveBeenCalledWith(404);
        expect(mockRes.json).toHaveBeenCalledWith({
            isSuccess: false,
            message: 'Batch not found'
        });
    });

    it('should return 404 when validateBatchOwnership throws an error', async () => {
        // Arrange
        const req = {...mockReq};
        ManifestItemService.validateBatchOwnership = jest.fn().mockRejectedValue(new Error('Database error'));

        // Action
        await batchController.getBatchByBatchNo(req, mockRes);

        // Assert
        expect(ManifestItemService.validateBatchOwnership).toHaveBeenCalledWith(mockMerchantName, mockBatchNo);
        expect(mockRes.status).toHaveBeenCalledWith(500);
        expect(mockRes.json).toHaveBeenCalledWith({
            isSuccess: false,
            message: 'Database error'
        });
    });

    it('should handle missing merchant info gracefully', async () => {
        // Arrange
        const req = {
            params: {
                batchNo: mockBatchNo
            },
            merchant: {} // Missing merchant_name
        };

        // Action
        await batchController.getBatchByBatchNo(req, mockRes);

        // Assert
        expect(mockRes.status).toHaveBeenCalledWith(500);
        expect(mockRes.json).toHaveBeenCalledTimes(1);
    });
});

describe('getBatchParcelsPaging', () => {
    const mockReq = {
        params: {
            batchNo: 'batchNo'
        },
        query: {
            offset: 10,
            limit: 10
        },
        merchant: {
            merchant_name: aesUtils.CrtCounterEncrypt('merchant_name')
        }
    };
    const mockRes = {
        json: jest.fn(),
    };
    mockRes.status = jest.fn().mockReturnValue(mockRes);

    it('should return 500 if error occur', async () => {
        // Arrange

        // Action
        await batchController.getBatchParcelsPaging(null, mockRes);

        // Assert
        expect(mockRes.status).toHaveBeenCalledWith(500);
        expect(mockRes.json).toHaveBeenCalledTimes(1);
    });

    it('should return 200 if success', async () => {
        // Arrange
        ManifestItemService.getBatchParcelsPaging = jest.fn().mockReturnValue({ 
            isSuccess: true,
            data: [{id: 'PXLUS0000121GNJ16B',
            tracking_id: '800351856999',
            service_option: 'standard',
            country: 'South Korea',
            order_number: 'THG1234',
            recipient_first_name: '문태웅',
            recipient_last_name: 'Zheng',
            lmd: 'Hanjin',
            tracking_status: [{
                status: 'Booked',
                timestamp: '2021-10-10 12:22:22'
            }],
            latest_tracking_status: 'LMD receive booking',
            cancellable: true }]
        });
        ShipmentService.getMAWBStatusByGaylordNos = jest.fn().mockResolvedValue([]);

        // Action
        await batchController.getBatchParcelsPaging(mockReq, mockRes);

        // Assert
        expect(mockRes.status).toHaveBeenCalledWith(200);
        expect(mockRes.json).toHaveBeenCalledTimes(1);
    });
});

describe('test getBatchShipments', () => {
    const mockRes = {
        json: jest.fn(),
    };
    mockRes.status = jest.fn().mockReturnValue(mockRes);

    test('test getBatchShipments pass', async () => {
        // Arrange
        const req = {
            merchant: {
                merchant_name: 'merchant_name' 
            },
            query: {}
        }
        const batchDate = '********';
        const shipmentType = 'domestic';
        const merchantAccount = '10536d';

        ManifestItemService.getBatchInfoByMerchant = jest.fn().mockResolvedValue({
            success: true,
            message: [{
                PLS_batch_no: `${batchDate}-${shipmentType}-x-${merchantAccount}`,
            }]
        })

        reportUtils.formatBatchNo = jest.fn().mockReturnValue('data');

        // Act
        await batchController.getBatchShipments(req, mockRes);

        // Assert
        expect(mockRes.json).toHaveBeenCalledWith({
            message: [{
                PLS_batch_no: '********-domestic-x-10536d',
                new_batch_no: '000230530d'
            }],
            success: true
        })
    })

    test('test getBatchShipments failed', async () => {
        // Arrange
        const req = {
            merchant: {
                merchant_name: 'merchant_name' 
            },
            query: {}
        }

        ManifestItemService.getBatchInfoByMerchant = jest.fn().mockRejectedValue('Error');

        // Act
        await batchController.getBatchShipments(req, mockRes);

        // Assert
        expect(console.log).toHaveBeenCalledWith('batch_controller getBatchShipments', 'Error');
        expect(mockRes.json).toHaveBeenCalledWith({
            success: false,
            message: 'Something went wrong: Error occur when get batch shipments'
        });
    })
})


describe('test generateRejectedParcels', () => {
    const mockRes = {
        json: jest.fn(),
        setHeader: jest.fn(),
        end: jest.fn()
    };
    mockRes.status = jest.fn().mockReturnValue(mockRes);

    test('test generateRejectedParcels pass', async () => {
        // Arrange
        const req = {
            body: {}
        }
        bookingService.getParcelsBuffer = jest.fn();

        // Act 
        await batchController.generateRejectedParcels(req, mockRes);

        // Arrange
        expect(mockRes.json).toHaveBeenCalledWith({
            success: false,
            message: 'Missing Rejected Parcels JSON'
        });
        expect(mockRes.end).toHaveBeenCalledWith(null);
    })

    test('test generateRejectedParcels fail', async () => {
        // Arrange
        bookingService.getParcelsBuffer = jest.fn();

        // Act 
        await batchController.generateRejectedParcels(undefined, mockRes);

        // Arrange
        expect(mockRes.json).toHaveBeenCalledWith({
            success: false,
            message: "Cannot read properties of undefined (reading 'body')"
        });
    })
})