const ManifestItemService = require('../../../merchant/services/manifest-item-services');
const StatusMappingService = require('../../../merchant/services/statusMappingService');
const aesUtils = require('../../../merchant/models/aesUtils');
const { parcelStatus } = require('../../../merchant/const/enum');
const AzureStorageQueue = require('../../../merchant/utilities/azure-queue-storage');
const ENUM = require('../../../merchant/const/enum');
const { getBatchOrder } = require('../../../merchant/utilities/queryUtils');
const dateUtils = require('../../../merchant/utilities/dateUtils');

ManifestItemService.manifestItemDao = {
  find: jest.fn(),
  updateItemByField: jest.fn(),
  getItem: jest.fn(),
  patchItem: jest.fn(),
};

ManifestItemService.merchantDao = {
  find: jest.fn(),
};

const currentDate = new Date('2020-03-20');

beforeEach(() => {
  jest.clearAllMocks();
  global.Date = jest.fn(() => currentDate);
});
console.log = jest.fn();

describe('it should test searchParcels()', () => {
  test('should reject', async () => {
    //Arrange
    ManifestItemService.manifestItemDao.find.mockRejectedValue('error');
    //Act
    try {
      await ManifestItemService.searchParcels(
        'searchKeyWord',
        'merchant_name'
      );
    } catch (error) {
      //Assert
      expect(error).toBe('error');
    }
  });
  test('should resolve data', async () => {
    //Arrange
    ManifestItemService.manifestItemDao.find.mockResolvedValue([
      {
        tracking_id: 'test',
      },
    ]);
    //Act
    const rs = await ManifestItemService.searchParcels(
      'searchKeyWord',
      'merchant_name'
    );
    //Assert
    expect(rs).toEqual([
      {
        tracking_id: 'test',
      },
    ]);
  });
});
describe('it should test getParcelById()', () => {
  test('getParcelById should reject', async () => {
    //Arrange
    ManifestItemService.manifestItemDao.getItem.mockRejectedValue('error');
    //Act
    try {
      await ManifestItemService.getParcelById('id');
    } catch (error) {
      //Assert
      expect(error).toBe('error');
    }
  });
});
describe('test findMP2ParcelsByBookingTime function', () => {
  test('should return data', async () => {
    // Arrange
    ManifestItemService.manifestItemDao.find = jest
      .fn()
      .mockResolvedValue([]);
    const bookingTimeArr = '2020-03-20';
    // Act
    const result = await ManifestItemService.findMP2ParcelsByBookingTime(
      bookingTimeArr
    );
    // Assert
    expect(result).toEqual([]);
    expect(ManifestItemService.manifestItemDao.find).toHaveBeenCalledWith({
      parameters: [
        {
          name: '@bookingPlatform',
          value: 'MP',
        },
        {
          name: '@bookingTimeArr',
          value: '2020-03-20',
        },
      ],
      query:
        `SELECT * FROM c WHERE c.booking_platform = @bookingPlatform
        AND IS_DEFINED(c.csvFileName) AND ARRAY_CONTAINS(@bookingTimeArr, c.tracking_status[0].date)
        ${getBatchOrder()}`,
    });
  });
});
describe('test findSplitParcelsByBookingTime function', () => {
  test('should return data', async () => {
    // Arrange
    ManifestItemService.manifestItemDao.find = jest
      .fn()
      .mockResolvedValue([]);
    const bookingTimeArr = '2020-03-20';
    // Act
    const result = await ManifestItemService.findSplitParcelsByBookingTime(
      bookingTimeArr
    );
    // Assert
    expect(result).toEqual([]);
    expect(ManifestItemService.manifestItemDao.find).toHaveBeenCalledWith({
      parameters: [
        {
          name: '@bookingTimeArr',
          value: '2020-03-20',
        },
      ],
      query: `SELECT * FROM c WHERE IS_DEFINED(c.parent_id)
        AND c.parent_id != c.id AND ARRAY_CONTAINS(@bookingTimeArr, c.tracking_status[0].date)
        ${getBatchOrder()}`,
    });
  });
});
describe('it should test isProcessRejectedBooking()', () => {
  test('should return false', async () => {
    //Arrange
    const data = {
      id: 1234,
      tracking_id: 1234,
      tracking_status: [
        {
          status: StatusMappingService.getManifestStatus(parcelStatus.booked),
        },
        {
          status: StatusMappingService.getManifestStatus(
            parcelStatus.lmd_receive_booking
          ),
        },
        {
          status: StatusMappingService.getManifestStatus(
            parcelStatus.lmd_reject_booking
          ),
        },
      ],
    };
    //Act
    let result = ManifestItemService.isProcessRejectedBooking(data);
    // Assert
    expect(result).toEqual(false);
  });
  test('should return true', async () => {
    //Arrange
    const data = {
      id: 1234,
      tracking_id: 1234,
      tracking_status: [
        {
          status: StatusMappingService.getManifestStatus(parcelStatus.booked),
        },
        {},
        {
          status: StatusMappingService.getManifestStatus(
            parcelStatus.lmd_reject_booking
          ),
        },
      ],
    };
    //Act
    let result = ManifestItemService.isProcessRejectedBooking(data);
    // Assert
    expect(result).toEqual(true);
  });
});
describe('Test getSortCode function', () => {
  afterEach(() => {
    jest.resetAllMocks(); // Reset mocks after each test
  });
  test('When calling function, return value as expected', async () => {
    const parcel = {
      id: 'PLX123',
      service_option: 'standard',
      incoterm: 'DDU',
      destination_group: 'DG_HJN_ICN',
    };
    const result = ManifestItemService.getSortCode(parcel);
    expect(result).toBe('10120101');
  });
});
describe('Test findBookingsByIdsOrTrackingIds function', () => {
  afterEach(() => {
    jest.resetAllMocks(); // Reset mocks after each test
  });
  test('When calling function, return value as expected', async () => {
    const ids = ['PLX123', 'PLX456'];
    const trackingIds = ['PLX789', 'PLX012'];
    ManifestItemService.manifestItemDao.find.mockResolvedValue([
      {
        id: 'PLX123',
        tracking_id: 'PLX789',
      },
      {
        id: 'PLX456',
        tracking_id: 'PLX012',
      },
    ]);
    const result = await ManifestItemService.findBookingsByIdsOrTrackingIds(
      ids,
      trackingIds
    );
    expect(result).toEqual([
      {
        id: 'PLX123',
        tracking_id: 'PLX789',
      },
      {
        id: 'PLX456',
        tracking_id: 'PLX012',
      },
    ]);
  });
});
describe('Test getBatchParcelsPaging', () => {
  test('getBatchParcelsPaging retuns successfully', async () => {
    //Arrange
    const mockManifestItems = [
      {
        country: aesUtils.CrtCounterEncrypt('country'),
        order_number: 'order_number',
        recipient_first_name: aesUtils.CrtCounterEncrypt(
          'recipient_first_name'
        ),
        recipient_last_name: aesUtils.CrtCounterEncrypt(
          'recipient_last_name'
        ),
        merchant_name: 'merchant name',
        PLS_batch_no: 'PLS_batch_no',
      },
      {
        country: aesUtils.CrtCounterEncrypt('country2'),
        order_number: 'order_number2',
        recipient_first_name: aesUtils.CrtCounterEncrypt(
          'recipient_first_name2'
        ),
        recipient_last_name: aesUtils.CrtCounterEncrypt(
          'recipient_last_name2'
        ),
        merchant_name: 'merchant name',
        PLS_batch_no: 'PLS_batch_no',
      },
    ];
    ManifestItemService.manifestItemDao.find.mockResolvedValue(
      mockManifestItems
    );
    //Act
    const result = await ManifestItemService.getBatchParcelsPaging(
      'merchant name',
      'PLS_batch_no',
      0,
      1
    );
    //Assert
    expect(result.data.length).toBe(1);
    expect(result.total).toBe(2);
    const parcel = result.data[0];
    expect(parcel.country).toEqual('country');
    expect(parcel.order_number).toEqual('order_number');
    expect(parcel.recipient_first_name).toEqual('recipient_first_name');
    expect(parcel.recipient_last_name).toEqual('recipient_last_name');
  });
  test('getBatchParcelsPaging retuns no parcel', async () => {
    //Arrange
    ManifestItemService.manifestItemDao.find.mockResolvedValue([]);
    //Act
    const result = await ManifestItemService.getBatchParcelsPaging(
      'merchant name',
      'PLS_batch_no',
      0,
      1
    );
    //Assert
    expect(result.data).toEqual([]);
    expect(result.total).toBe(0);
  });
});
describe('Test getBatchInfoByMerchant fucntion ', () => {
  test('getBatchInfoByMerchant returns successfully', async () => {
    //Arrange
    const mockParcels = [
      {
        PLS_batch_no: 'PLS_batch_no',
        _ts: 1705047769,
      },
    ];
    const mockResult = [
      {
        PLS_batch_no: 'PLS_batch_no',
        _ts: 1705047769,
        batch_size: 1,
      },
    ];
    ManifestItemService.manifestItemDao.find.mockResolvedValue(mockParcels);
    //Act
    const result = await ManifestItemService.getBatchInfoByMerchant(
      'merchant name',
      1,
      1
    );
    //Assert
    expect(result.message).toEqual(mockResult);
  });
  test('getBatchInfoByMerchant throw error when find no parcel', async () => {
    //Arrange
    ManifestItemService.manifestItemDao.find.mockResolvedValue([]);
    //Act
    //Assert
    await expect(async () => {
      await ManifestItemService.getBatchInfoByMerchant('merchant name', 1, 1);
    }).rejects.toThrow('You have no batchShipments');
  });
});

describe('triggerCat1MerchantBooking', () => {
  test('Should do nothing when parcel does not have dim weight', async () => {
    // Arrange
    const parcel = {
      merchant_name: 'PRESCO TW UAT',
      has_dims_weight: false,
    };
    AzureStorageQueue.sendBase64Message = jest.fn().mockReturnValue(null);
    // Act
    await ManifestItemService.triggerCat1MerchantBooking(parcel);
    // Assert
    expect(AzureStorageQueue.sendBase64Message).not.toHaveBeenCalled();
  });
  test('Should do nothing when parcel does not have hscode and not njv sg', async () => {
    // Arrange
    const parcel = {
      merchant_name: 'PRESCO TW UAT',
      has_dims_weight: true,
      has_hscode: false,
      lmd: 'boxc',
    };
    AzureStorageQueue.sendBase64Message = jest.fn().mockReturnValue(null);
    // Act
    await ManifestItemService.triggerCat1MerchantBooking(parcel);
    // Assert
    expect(AzureStorageQueue.sendBase64Message).not.toHaveBeenCalled();
  });
  test('Should trigger LMD when parcel does not have hscode but lmd is njv sg', async () => {
    // Arrange
    const parcel = {
      merchant_name: 'PRESCO TW UAT',
      has_dims_weight: true,
      has_hscode: false,
      lmd: ENUM.lmdProviders.NINJAVAN_SINGAPORE,
    };
    AzureStorageQueue.sendBase64Message = jest.fn().mockReturnValue(null);
    // Act
    await ManifestItemService.triggerCat1MerchantBooking(parcel);
    // Assert
    expect(AzureStorageQueue.sendBase64Message).toHaveBeenCalled();
  });
  test('Should trigger LMD when parcel have hscode', async () => {
    // Arrange
    const parcel = {
      merchant_name: 'PRESCO TW UAT',
      has_dims_weight: true,
      has_hscode: true,
    };
    AzureStorageQueue.sendBase64Message = jest.fn().mockReturnValue(null);
    // Act
    await ManifestItemService.triggerCat1MerchantBooking(parcel);
    // Assert
    expect(AzureStorageQueue.sendBase64Message).toHaveBeenCalled();
  });
});

describe('getLmdReceivedBookings', () => {
  test('it should return correct data', async () => {
    ManifestItemService.manifestItemDao.find = jest.fn().mockResolvedValue([{ destination_group: 'VIE' }, { destination_group: 'VIE' }]);

    const data = await ManifestItemService.getLmdReceivedBookings('SG', '"2024-11-25T00:00:00.000Z"', '2024-12-01T23:59:59.999Z', []);

    expect(data).toEqual({ VIE: 2 });
  });
});

// ... existing code ...

describe('generateLastSixMonths', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-03-20'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test('should return array of 6 months', () => {
    const result = ManifestItemService.generateLastSixMonths();
    expect(result).toHaveLength(6);
  });

  test('should return months in correct format (MMM\'YY)', () => {
    const result = ManifestItemService.generateLastSixMonths();
    const expectedMonths = [
      { month: 'Mar\'24', sortOrder: 202403 },
      { month: 'Feb\'24', sortOrder: 202402 },
      { month: 'Jan\'24', sortOrder: 202401 },
      { month: 'Dec\'23', sortOrder: 202312 },
      { month: 'Nov\'23', sortOrder: 202311 },
      { month: 'Oct\'23', sortOrder: 202310 }
    ];
    expect(result).toEqual(expectedMonths);
  });

  test('should calculate correct sort orders', () => {
    const result = ManifestItemService.generateLastSixMonths();
    result.forEach(month => {
      const [monthStr, yearStr] = month.month.split('\'');
      const year = parseInt('20' + yearStr);
      const monthNum = new Date(`${monthStr} 1, 2000`).getMonth() + 1;
      const expectedSortOrder = year * 100 + monthNum;
      expect(month.sortOrder).toBe(expectedSortOrder);
    });
  });

  test('should return months in descending order (most recent first)', () => {
    const result = ManifestItemService.generateLastSixMonths();
    for (let i = 0; i < result.length - 1; i++) {
      expect(result[i].sortOrder).toBeGreaterThan(result[i + 1].sortOrder);
    }
  });
});

describe('initializeMonthlyData', () => {
  test('should create object with correct structure for each month', () => {
    const months = [
      { month: 'Mar\'24', sortOrder: 202403 },
      { month: 'Feb\'24', sortOrder: 202402 },
      { month: 'Jan\'24', sortOrder: 202401 }
    ];

    const result = ManifestItemService.initializeMonthlyData(months);

    // Check that each month exists as a key
    expect(result).toHaveProperty('Mar\'24');
    expect(result).toHaveProperty('Feb\'24');
    expect(result).toHaveProperty('Jan\'24');

    // Check the structure of each month's data
    expect(result['Mar\'24']).toEqual({
      month: 'Mar\'24',
      sortOrder: 202403
    });
    expect(result['Feb\'24']).toEqual({
      month: 'Feb\'24',
      sortOrder: 202402
    });
    expect(result['Jan\'24']).toEqual({
      month: 'Jan\'24',
      sortOrder: 202401
    });
  });

  test('should handle empty input array', () => {
    const result = ManifestItemService.initializeMonthlyData([]);
    expect(result).toEqual({});
  });

  test('should preserve all properties from input months', () => {
    const months = [
      { month: 'Mar\'24', sortOrder: 202403, extraProp: 'test' }
    ];

    const result = ManifestItemService.initializeMonthlyData(months);

    expect(result['Mar\'24']).toEqual({
      month: 'Mar\'24',
      sortOrder: 202403,
    });
  });

  test('should create independent objects for each month', () => {
    const months = [
      { month: 'Mar\'24', sortOrder: 202403 },
      { month: 'Feb\'24', sortOrder: 202402 }
    ];

    const result = ManifestItemService.initializeMonthlyData(months);

    // Modify one month's data
    result['Mar\'24'].sortOrder = 999999;

    // Check that other month's data remains unchanged
    expect(result['Feb\'24'].sortOrder).toBe(202402);
  });
}); describe('initializeMonthlyData', () => {
  test('should create object with correct structure for each month', () => {
    const months = [
      { month: 'Mar\'24', sortOrder: 202403 },
      { month: 'Feb\'24', sortOrder: 202402 },
      { month: 'Jan\'24', sortOrder: 202401 }
    ];

    const result = ManifestItemService.initializeMonthlyData(months);

    // Check that each month exists as a key
    expect(result).toHaveProperty('Mar\'24');
    expect(result).toHaveProperty('Feb\'24');
    expect(result).toHaveProperty('Jan\'24');

    // Check the structure of each month's data
    expect(result['Mar\'24']).toEqual({
      month: 'Mar\'24',
      sortOrder: 202403
    });
    expect(result['Feb\'24']).toEqual({
      month: 'Feb\'24',
      sortOrder: 202402
    });
    expect(result['Jan\'24']).toEqual({
      month: 'Jan\'24',
      sortOrder: 202401
    });
  });

  test('should handle empty input array', () => {
    const result = ManifestItemService.initializeMonthlyData([]);
    expect(result).toEqual({});
  });

  test('should preserve all properties from input months', () => {
    const months = [
      { month: 'Mar\'24', sortOrder: 202403, extraProp: 'test' }
    ];

    const result = ManifestItemService.initializeMonthlyData(months);

    expect(result['Mar\'24']).toEqual({
      month: 'Mar\'24',
      sortOrder: 202403,
    });
  });

  test('should create independent objects for each month', () => {
    const months = [
      { month: 'Mar\'24', sortOrder: 202403 },
      { month: 'Feb\'24', sortOrder: 202402 }
    ];

    const result = ManifestItemService.initializeMonthlyData(months);

    // Modify one month's data
    result['Mar\'24'].sortOrder = 999999;

    // Check that other month's data remains unchanged
    expect(result['Feb\'24'].sortOrder).toBe(202402);
  });
});

describe('sortAndCleanMonthlyData', () => {
  test('should sort months by sortOrder and remove sortOrder property', () => {
    const monthlyData = {
      'Mar\'24': { month: 'Mar\'24', sortOrder: 202403, SG: 10 },
      'Jan\'24': { month: 'Jan\'24', sortOrder: 202401, SG: 5 },
      'Feb\'24': { month: 'Feb\'24', sortOrder: 202402, SG: 8 }
    };

    const result = ManifestItemService.sortAndCleanMonthlyData(monthlyData);

    // Check array length
    expect(result).toHaveLength(3);

    // Check sorting order (ascending by sortOrder)
    expect(result[0].month).toBe('Jan\'24');
    expect(result[1].month).toBe('Feb\'24');
    expect(result[2].month).toBe('Mar\'24');

    // Check that sortOrder is removed
    result.forEach(item => {
      expect(item).not.toHaveProperty('sortOrder');
    });

    // Check that other properties are preserved
    expect(result[0].SG).toBe(5);
    expect(result[1].SG).toBe(8);
    expect(result[2].SG).toBe(10);
  });

  test('should handle empty input object', () => {
    const result = ManifestItemService.sortAndCleanMonthlyData({});
    expect(result).toEqual([]);
  });

  test('should preserve all other properties except sortOrder', () => {
    const monthlyData = {
      'Mar\'24': {
        month: 'Mar\'24',
        sortOrder: 202403,
        SG: 10,
        MY: 5,
        extraProp: 'test'
      }
    };

    const result = ManifestItemService.sortAndCleanMonthlyData(monthlyData);

    expect(result[0]).toEqual({
      month: 'Mar\'24',
      SG: 10,
      MY: 5,
      extraProp: 'test'
    });
  });

  test('should handle months with same sortOrder', () => {
    const monthlyData = {
      'Mar\'24': { month: 'Mar\'24', sortOrder: 202403, SG: 10 },
      'Mar\'24_2': { month: 'Mar\'24_2', sortOrder: 202403, SG: 15 }
    };

    const result = ManifestItemService.sortAndCleanMonthlyData(monthlyData);

    // Both items should be present and sortOrder should be removed
    expect(result).toHaveLength(2);
    result.forEach(item => {
      expect(item).not.toHaveProperty('sortOrder');
    });
  });
});

describe('queryMPDashboardData', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-03-20'));
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  test('should query data for last 6 months', async () => {
    // Arrange
    const merchantName = 'testMerchant';
    const mockParcels = [
      { id: '1', order_date: '2024-03-15', country: 'SG', tracking_status: [] },
      { id: '2', order_date: '2024-02-15', country: 'MY', tracking_status: [] }
    ];
    ManifestItemService.manifestItemDao.find = jest.fn().mockResolvedValue(mockParcels);

    // Act
    const result = await ManifestItemService.queryMPDashboardData(merchantName);

    // Assert
    expect(ManifestItemService.manifestItemDao.find).toHaveBeenCalledWith({
      query: expect.stringContaining('SELECT'),
      parameters: [
        { name: '@merchantName', value: merchantName },
        { name: '@startDate', value: '2023-10-01' }, // 6 months ago
        { name: '@endDate', value: '2024-03-31' }    // End of current month
      ]
    });
    expect(result).toEqual(mockParcels);
  });

  test('should handle empty result', async () => {
    // Arrange
    const merchantName = 'testMerchant';
    ManifestItemService.manifestItemDao.find = jest.fn().mockResolvedValue([]);

    // Act
    const result = await ManifestItemService.queryMPDashboardData(merchantName);

    // Assert
    expect(result).toEqual([]);
    expect(ManifestItemService.manifestItemDao.find).toHaveBeenCalled();
  });

  test('should handle database error', async () => {
    // Arrange
    const merchantName = 'testMerchant';
    const error = new Error('Database error');
    ManifestItemService.manifestItemDao.find = jest.fn().mockRejectedValue(error);

    // Act & Assert
    await expect(ManifestItemService.queryMPDashboardData(merchantName))
      .rejects.toThrow('Database error');
  });

  test('should query correct fields', async () => {
    // Arrange
    const merchantName = 'testMerchant';
    ManifestItemService.manifestItemDao.find = jest.fn().mockResolvedValue([]);

    // Act
    await ManifestItemService.queryMPDashboardData(merchantName);

    // Assert
    const expectedQuery = `
        SELECT 
          c.id,
          c.order_date,
          c.country,
          c.tracking_status
        FROM c
        WHERE c.merchant_name = @merchantName
        AND c.order_date >= @startDate
        AND c.order_date <= @endDate
      `;

    expect(ManifestItemService.manifestItemDao.find).toHaveBeenCalledWith({
      query: expectedQuery,
      parameters: [
        { name: '@merchantName', value: merchantName },
        { name: '@startDate', value: '2023-10-01' },
        { name: '@endDate', value: '2024-03-31' }
      ]
    });
  });
  test('should handle date range correctly', async () => {
    // Arrange
    const merchantName = 'testMerchant';
    ManifestItemService.manifestItemDao.find = jest.fn().mockResolvedValue([]);

    // Act
    await ManifestItemService.queryMPDashboardData(merchantName);

    // Assert
    const expectedStartDate = '2023-10-01'; // 6 months ago from March 2024
    const expectedEndDate = '2024-03-31';   // End of March 2024

    expect(ManifestItemService.manifestItemDao.find).toHaveBeenCalledWith(
      expect.objectContaining({
        parameters: expect.arrayContaining([
          { name: '@startDate', value: expectedStartDate },
          { name: '@endDate', value: expectedEndDate }
        ])
      })
    );
  });
});

describe('processDataItem', () => {
  let monthlyData;
  let countryCache;

  beforeEach(() => {
    // Mock dateUtils.formatDateTime to ensure consistent date formatting
    jest.spyOn(dateUtils, 'formatDateTime').mockImplementation((date, format) => {
      if (format === 'MMM YYYY') {
        return 'Mar 2024';
      }
      return date;
    });

    monthlyData = {
      'Mar\'24': { month: 'Mar\'24', sortOrder: 202403 },
      'Feb\'24': { month: 'Feb\'24', sortOrder: 202402 },
      'Jan\'24': { month: 'Jan\'24', sortOrder: 202401 }
    };
    countryCache = new Map();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('should process valid item and update monthly data', () => {
    // Arrange
    const item = {
      country: 'encrypted_SG',
      order_date: '2024-03-15'
    };
    jest.spyOn(aesUtils, 'CrtCounterDecrypt').mockReturnValue('SG');

    // Act
    ManifestItemService.processDataItem(item, monthlyData, countryCache);

    // Assert
    expect(monthlyData['Mar\'24'].SG).toBe(1);
    expect(countryCache.get('encrypted_SG')).toBe('SG');
    expect(aesUtils.CrtCounterDecrypt).toHaveBeenCalledWith('encrypted_SG');
  });

  test('should skip item without country', () => {
    // Arrange
    const item = {
      order_date: '2024-03-15'
    };

    // Act
    ManifestItemService.processDataItem(item, monthlyData, countryCache);

    // Assert
    expect(monthlyData['Mar\'24']).not.toHaveProperty('SG');
  });

  test('should skip item without order_date', () => {
    // Arrange
    const item = {
      country: 'encrypted_SG'
    };

    // Act
    ManifestItemService.processDataItem(item, monthlyData, countryCache);

    // Assert
    expect(monthlyData['Mar\'24']).not.toHaveProperty('SG');
  });

  test('should use cached country value if available', () => {
    // Arrange
    const item = {
      country: 'encrypted_SG',
      order_date: '2024-03-15'
    };
    countryCache.set('encrypted_SG', 'SG');
    jest.spyOn(aesUtils, 'CrtCounterDecrypt'); // Don't mock return value since it shouldn't be called

    // Act
    ManifestItemService.processDataItem(item, monthlyData, countryCache);

    // Assert
    expect(monthlyData['Mar\'24'].SG).toBe(1);
    expect(aesUtils.CrtCounterDecrypt).not.toHaveBeenCalled();
  });

  test('should increment existing country count', () => {
    // Arrange
    const item = {
      country: 'encrypted_SG',
      order_date: '2024-03-15'
    };
    monthlyData['Mar\'24'].SG = 5;
    jest.spyOn(aesUtils, 'CrtCounterDecrypt').mockReturnValue('SG');

    // Act
    ManifestItemService.processDataItem(item, monthlyData, countryCache);

    // Assert
    expect(monthlyData['Mar\'24'].SG).toBe(6);
  });

  test('should skip item with month outside range', () => {
    // Arrange
    const item = {
      country: 'encrypted_SG',
      order_date: '2023-09-15' // Outside our 6-month range
    };
    jest.spyOn(dateUtils, 'formatDateTime').mockReturnValueOnce('Sep 2023');
    jest.spyOn(aesUtils, 'CrtCounterDecrypt').mockReturnValue('SG');

    // Act
    ManifestItemService.processDataItem(item, monthlyData, countryCache);

    // Assert
    expect(monthlyData['Mar\'24']).not.toHaveProperty('SG');
  });

  test('should handle multiple countries in same month', () => {
    // Arrange
    const items = [
      { country: 'encrypted_SG', order_date: '2024-03-15' },
      { country: 'encrypted_MY', order_date: '2024-03-15' }
    ];
    jest.spyOn(aesUtils, 'CrtCounterDecrypt')
      .mockReturnValueOnce('SG')
      .mockReturnValueOnce('MY');

    // Act
    items.forEach(item => {
      ManifestItemService.processDataItem(item, monthlyData, countryCache);
    });

    // Assert
    expect(monthlyData['Mar\'24'].SG).toBe(1);
    expect(monthlyData['Mar\'24'].MY).toBe(1);
  });
});

describe('processDashboardData', () => {
  beforeEach(() => {
    // Mock generateLastSixMonths to return consistent data
    jest.spyOn(ManifestItemService, 'generateLastSixMonths').mockReturnValue([
      { month: 'Mar\'24', sortOrder: 202403 },
      { month: 'Feb\'24', sortOrder: 202402 },
      { month: 'Jan\'24', sortOrder: 202401 }
    ]);

    // Mock dateUtils.formatDateTime to handle different dates
    jest.spyOn(dateUtils, 'formatDateTime').mockImplementation((date, format) => {
      if (format === 'MMM YYYY') {
        const d = new Date(date);
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const month = months[d.getMonth()];
        const year = d.getFullYear().toString().slice(-2);
        return `${month}'${year}`; // Note the single quote and two-digit year
      }
      return date;
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('should handle undefined data', async () => {
    const result = await ManifestItemService.processDashboardData(undefined);
    expect(result).toEqual({
      parcelVolume: [
        { month: 'Mar\'24' },
        { month: 'Feb\'24' },
        { month: 'Jan\'24' }
      ]
    });
  });

  test('should handle empty array', async () => {
    const result = await ManifestItemService.processDashboardData([]);
    expect(result).toEqual({
      parcelVolume: [
        { month: 'Mar\'24' },
        { month: 'Feb\'24' },
        { month: 'Jan\'24' }
      ]
    });
  });

  test('should process valid data correctly', async () => {
    // Arrange
    const data = [
      { country: 'encrypted_SG', order_date: '2024-03-15' },
      { country: 'encrypted_MY', order_date: '2024-03-15' },
      { country: 'encrypted_SG', order_date: '2024-02-15' }
    ];

    // Mock CrtCounterDecrypt to return different values for each call
    const decryptSpy = jest.spyOn(aesUtils, 'CrtCounterDecrypt');
    decryptSpy.mockImplementation((country) => {
      if (country === 'encrypted_SG') return 'SG';
      if (country === 'encrypted_MY') return 'MY';
      return country;
    });

    // Act
    const result = await ManifestItemService.processDashboardData(data);

    // Assert
    expect(result).toEqual({
      parcelVolume: [
        { month: 'Jan\'24' },
        { month: 'Feb\'24' },
        { month: 'Mar\'24' }
      ]
    });
  });

  test('should handle items outside date range', async () => {
    // Arrange
    const data = [
      { country: 'encrypted_SG', order_date: '2023-09-15' }, // Outside range
      { country: 'encrypted_MY', order_date: '2024-03-15' }  // Inside range
    ];

    // Mock CrtCounterDecrypt to return different values for each call
    const decryptSpy = jest.spyOn(aesUtils, 'CrtCounterDecrypt');
    decryptSpy.mockImplementation((country) => {
      if (country === 'encrypted_SG') return 'SG';
      if (country === 'encrypted_MY') return 'MY';
      return country;
    });

    // Act
    const result = await ManifestItemService.processDashboardData(data);

    // Assert
    expect(result).toEqual({
      parcelVolume: [
        { month: 'Jan\'24' },
        { month: 'Feb\'24' },
        { month: 'Mar\'24' }
      ]
    });
  });

  test('should handle invalid items', async () => {
    // Arrange
    const data = [
      { country: 'encrypted_SG' }, // Missing order_date
      { order_date: '2024-03-15' }, // Missing country
      { country: 'encrypted_MY', order_date: '2024-03-15' } // Valid item
    ];

    // Mock CrtCounterDecrypt to return different values for each call
    const decryptSpy = jest.spyOn(aesUtils, 'CrtCounterDecrypt');
    decryptSpy.mockImplementation((country) => {
      if (country === 'encrypted_SG') return 'SG';
      if (country === 'encrypted_MY') return 'MY';
      return country;
    });

    // Act
    const result = await ManifestItemService.processDashboardData(data);

    // Assert
    expect(result).toEqual({
      parcelVolume: [
        { month: 'Jan\'24' },
        { month: 'Feb\'24' },
        { month: 'Mar\'24' }
      ]
    });
  });

  test('should sort months correctly', async () => {
    // Arrange
    const data = [
      { country: 'encrypted_SG', order_date: '2024-03-15' },
      { country: 'encrypted_MY', order_date: '2024-01-15' },
      { country: 'encrypted_SG', order_date: '2024-02-15' }
    ];

    // Mock CrtCounterDecrypt to return different values for each call
    const decryptSpy = jest.spyOn(aesUtils, 'CrtCounterDecrypt');
    decryptSpy.mockImplementation((country) => {
      if (country === 'encrypted_SG') return 'SG';
      if (country === 'encrypted_MY') return 'MY';
      return country;
    });

    // Act
    const result = await ManifestItemService.processDashboardData(data);

    // Assert
    expect(result.parcelVolume).toEqual([
      { month: 'Jan\'24' },
      { month: 'Feb\'24' },
      { month: 'Mar\'24' }
    ]);
  });
});

describe('validateBatchOwnership', () => {
  const mockMerchantName = 'test_merchant';
  const mockBatchNo = '20230530-INTER-SIN01-1234567890';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should return true when merchant owns the batch', async () => {
    // Arrange
    ManifestItemService.manifestItemDao.find.mockResolvedValue([1]); // COUNT(1) returns 1

    // Act
    const result = await ManifestItemService.validateBatchOwnership(mockMerchantName, mockBatchNo);

    // Assert
    expect(result).toBe(true);
    expect(ManifestItemService.manifestItemDao.find).toHaveBeenCalledWith({
      query: `SELECT VALUE COUNT(1) FROM c
                WHERE c.merchant_name = @merchantName
                AND c.PLS_batch_no = @batchNo`,
      parameters: [
        {
          name: '@merchantName',
          value: mockMerchantName
        },
        {
          name: '@batchNo',
          value: mockBatchNo
        }
      ]
    });
  });

  test('should return false when merchant does not own the batch', async () => {
    // Arrange
    ManifestItemService.manifestItemDao.find.mockResolvedValue([0]); // COUNT(1) returns 0

    // Act
    const result = await ManifestItemService.validateBatchOwnership(mockMerchantName, mockBatchNo);

    // Assert
    expect(result).toBe(false);
    expect(ManifestItemService.manifestItemDao.find).toHaveBeenCalledWith({
      query: `SELECT VALUE COUNT(1) FROM c
                WHERE c.merchant_name = @merchantName
                AND c.PLS_batch_no = @batchNo`,
      parameters: [
        {
          name: '@merchantName',
          value: mockMerchantName
        },
        {
          name: '@batchNo',
          value: mockBatchNo
        }
      ]
    });
  });

  test('should return false when database query returns empty result', async () => {
    // Arrange
    ManifestItemService.manifestItemDao.find.mockResolvedValue([]);

    // Act
    const result = await ManifestItemService.validateBatchOwnership(mockMerchantName, mockBatchNo);

    // Assert
    expect(result).toBe(false);
  });

  test('should return false when database query returns null', async () => {
    // Arrange
    ManifestItemService.manifestItemDao.find.mockResolvedValue(null);

    // Act
    const result = await ManifestItemService.validateBatchOwnership(mockMerchantName, mockBatchNo);

    // Assert
    expect(result).toBe(false);
  });

  test('should return false when database query throws an error', async () => {
    // Arrange
    ManifestItemService.manifestItemDao.find.mockRejectedValue(new Error('Database error'));

    // Act
    const result = await ManifestItemService.validateBatchOwnership(mockMerchantName, mockBatchNo);

    // Assert
    expect(result).toBe(false);
    expect(console.log).toHaveBeenCalledWith(
      'manifest-item-services validateBatchOwnership',
      mockMerchantName,
      mockBatchNo,
      expect.any(Error)
    );
  });
});