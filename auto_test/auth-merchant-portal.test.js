const MerchantAuthen = require("../merchant/auth/auth-merchant-portal");
const MerchantService = require("../merchant/services/merchant-service");
const aesUtils = require('../merchant/models/aesUtils');

describe('Test injectMerchantInfo', () => {
  let merchantAuthen = null;

  beforeEach(() => {
    console.error = jest.fn();
    jest.clearAllMocks();
    merchantAuthen = new MerchantAuthen(null);
  });

  test("Should response 403 because user don't have permission", async () => {
    //Assign
    const req = {
      decoded: {
        emails: ['test_email']
      },
      url: "test_url"
    }
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    }
    const next = jest.fn()
    MerchantService.getMerchantByAccountEmail = jest.fn().mockResolvedValue([]);
    //Act
    await merchantAuthen.injectMerchantInfo(req, res, next)
    //Assert
    expect(next).not.toHaveBeenCalled();
    expect(console.error).not.toHaveBeenCalled();
    expect(res.status.mock.calls[0][0]).toBe(403);
    expect(res.json.mock.calls[0][0].error).toEqual(expect.any(String));
  })

  test('Should catch error then response status 403 if getMerchant failed', async () => {
    //Assign
    const req = {
      decoded: {
        emails: ['test_email']
      },
      url: "test_url"
    }
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    }
    const next = jest.fn()
    const errorMessage = "test get merchant fail error"
    MerchantService.getMerchantByAccountEmail = jest.fn().mockRejectedValue(errorMessage);
    //Act
    await merchantAuthen.injectMerchantInfo(req, res, next)
    //Assert
    expect(next).not.toHaveBeenCalled();
    expect(res.status.mock.calls[0][0]).toBe(403);
    expect(res.json.mock.calls[0][0].error).toEqual(expect.any(String));
  })


  test('Should inject merchant into request as expected', async () => {
    //Assign
    const req = {
      decoded: {
        emails: ['test_email']
      },
      url: "test_url"
    }
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    }
    const next = jest.fn()
    const testMerchant = { test: "test_merchant" }
    MerchantService.getMerchantByAccountEmail = jest.fn().mockResolvedValue([testMerchant]);
    //Act
    await merchantAuthen.injectMerchantInfo(req, res, next)
    //Assert
    expect(next).toHaveBeenCalled();
    expect(console.error).not.toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
    expect(req.merchant).toEqual(expect.any(Object));
    expect(req.merchant.test).toBe(testMerchant.test)
  })
});

describe("validateToken", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should validate the token', async () => {
    // Arrange
    MerchantAuthen.prototype.verifyJWT = jest.fn().mockReturnValue({
      exp: Date.now() + 10 * 1000,
      emails: ["<EMAIL>"]
    });
    MerchantAuthen.prototype.getPublicKey = jest.fn();
    MerchantAuthen.prototype.validateUserSession = jest.fn();

    const req = {
      headers: {
        authorization:
          "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************.DfK01oU_eNyR0rgGNsQkyJN9vMl1xHjZdvAGP993_m0"
      },
      decoded: {},
      cookies: {
        'connect.sid': '',
      }
    };

    const res = {
      status: jest.fn().mockReturnValue({ send: jest.fn() }),
      cookie: jest.fn()
    };

    const next = jest.fn();

    // Act
    await MerchantAuthen.prototype.validateToken(req, res, next);

    // Assert
    expect(next).toHaveBeenCalled();
  });

  test('should return response status 401 when the token is not valid', async () => {
    // Arrange
    MerchantAuthen.prototype.verifyJWT = jest.fn().mockReturnValue(false);
    MerchantAuthen.prototype.getPublicKey = jest.fn();
    MerchantAuthen.prototype.validateUserSession = jest.fn();

    const req = {
      headers: {
        authorization:
          "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************.DfK01oU_eNyR0rgGNsQkyJN9vMl1xHjZdvAGP993_m0"
      },
      decoded: {},
      cookies: {
        'connect.sid': '',
      }
    };

    const res = {
      status: jest.fn().mockReturnValue({ send: jest.fn() }),
      cookie: jest.fn()
    };

    const next = jest.fn();

    // Act
    await MerchantAuthen.prototype.validateToken(req, res, next);

    // Assert
    expect(res.status).toHaveBeenCalledWith(401)
  })

  test('should return response status 401 if function throw error', async () => {
    // Arrange
    MerchantAuthen.prototype.getPublicKey = jest.fn().mockRejectedValueOnce(new Error('Error'));

    const req = {
      headers: {
        authorization:
          "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************.DfK01oU_eNyR0rgGNsQkyJN9vMl1xHjZdvAGP993_m0"
      },
      decoded: {},
      cookies: {
        'connect.sid': '',
      }
    };

    const res = {
      status: jest.fn().mockReturnValue({ send: jest.fn() }),
      cookie: jest.fn()
    };

    const next = jest.fn();

    // Act
    await MerchantAuthen.prototype.validateToken(req, res, next);

    // Assert
    expect(res.status).toHaveBeenCalledWith(401)
  })
});

describe("validateTokenSocket", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should validate the token for socket', async () => {
    // Arrange
    MerchantAuthen.prototype.verifyJWT = jest.fn().mockReturnValue({
      exp: Date.now() + 10 * 1000,
      emails: ["<EMAIL>"]
    });
    MerchantAuthen.prototype.getPublicKey = jest.fn();

    const socket = {
      handshake: {
        auth: {
          authorization:
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************.DfK01oU_eNyR0rgGNsQkyJN9vMl1xHjZdvAGP993_m0"
        }
      },
      decoded: {},
    };

    const next = jest.fn();

    // Act
    await MerchantAuthen.prototype.validateTokenSocket(socket, next);

    // Assert
    expect(next).toHaveBeenCalled();
  });

  test('should throw err if verify token failed', async () => {
    // Arrange
    MerchantAuthen.prototype.verifyJWT = jest.fn().mockReturnValue(false);
    MerchantAuthen.prototype.getPublicKey = jest.fn();

    const socket = {
      handshake: {
        auth: {
          authorization:
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************.DfK01oU_eNyR0rgGNsQkyJN9vMl1xHjZdvAGP993_m0"
        }
      },
      decoded: {},
    };

    const next = jest.fn();

    // Act
    await MerchantAuthen.prototype.validateTokenSocket(socket, next);

    // Assert
    expect(next).toHaveBeenCalledWith(new Error('Unauthorized. The access token is invalid.'));
  });
});

describe('injectMerchantInfoSocket', () => {
  let socket;
  let next;

  beforeEach(() => {
    socket = {
      decoded: {
        emails: ['<EMAIL>']
      }
    };
    next = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should inject merchant info into socket and call next', async () => {
    const mockMerchant = { id: 1, name: 'Test Merchant' };
    const decryptedMerchant = { id: 1, name: 'Decrypted Test Merchant' };

    MerchantService.getMerchantByAccountEmail = jest.fn().mockResolvedValue([mockMerchant]);
    aesUtils.decryptMerchant = jest.fn().mockReturnValue(decryptedMerchant);

    await MerchantAuthen.prototype.injectMerchantInfoSocket(socket, next);

    expect(MerchantService.getMerchantByAccountEmail).toHaveBeenCalledWith('<EMAIL>');
    expect(aesUtils.decryptMerchant).toHaveBeenCalledWith(mockMerchant);
    expect(socket.merchant).toEqual(decryptedMerchant);
    expect(next).toHaveBeenCalledWith();
  });

  it('should call next with error if no merchants are found', async () => {
    MerchantService.getMerchantByAccountEmail = jest.fn().mockResolvedValue([]);

    await MerchantAuthen.prototype.injectMerchantInfoSocket(socket, next);

    expect(MerchantService.getMerchantByAccountEmail).toHaveBeenCalledWith('<EMAIL>');
    expect(next).toHaveBeenCalledWith(new Error('You have not permission to perform this action'));
  });

  it('should call next with error on exception', async () => {
    const errorMessage = 'Some error';
    MerchantService.getMerchantByAccountEmail = jest.fn().mockRejectedValue(new Error(errorMessage));

    await MerchantAuthen.prototype.injectMerchantInfoSocket(socket, next);

    expect(next).toHaveBeenCalledWith(expect.any(Error));
  });
});

describe('injectMerchantInfoUI', () => {
  let merchantAuthen = null;

  beforeEach(() => {
    console.error = jest.fn();
    jest.clearAllMocks();
    merchantAuthen = new MerchantAuthen(null);
  });

  test("Should response 403 because user don't have permission", async () => {
    // Assign
    const req = {
      decoded: {
        emails: ['test_email']
      },
      url: "test_url"
    };
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    const next = jest.fn();
    MerchantService.getMerchantByAccountEmail = jest.fn().mockResolvedValue([]);
    // Act
    await merchantAuthen.injectMerchantInfoUI(req, res, next);
    // Assert
    expect(next).not.toHaveBeenCalled();
    expect(console.error).not.toHaveBeenCalled();
    expect(res.status.mock.calls[0][0]).toBe(403);
    expect(res.json.mock.calls[0][0].error).toEqual(expect.any(String));
  });

  test('Should catch error then response status 403 if getMerchant failed', async () => {
    // Assign
    const req = {
      decoded: {
        emails: ['test_email']
      },
      url: "test_url"
    };
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    const next = jest.fn();
    const errorMessage = "test get merchant fail error";
    MerchantService.getMerchantByAccountEmail = jest.fn().mockRejectedValue(errorMessage);
    // Act
    await merchantAuthen.injectMerchantInfoUI(req, res, next);
    // Assert
    expect(next).not.toHaveBeenCalled();
    expect(res.status.mock.calls[0][0]).toBe(403);
    expect(res.json.mock.calls[0][0].error).toEqual(expect.any(String));
  });

  test('Should inject decrypted merchant into request as expected', async () => {
    // Assign
    const req = {
      decoded: {
        emails: ['test_email']
      },
      url: "test_url"
    };
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    const next = jest.fn();
    const testMerchant = { test: "test_merchant" };
    const decryptedMerchant = { test: "decrypted_test_merchant" };
    MerchantService.getMerchantByAccountEmail = jest.fn().mockResolvedValue([testMerchant]);
    aesUtils.decryptMerchant = jest.fn().mockReturnValue(decryptedMerchant);
    // Act
    await merchantAuthen.injectMerchantInfoUI(req, res, next);
    // Assert
    expect(next).toHaveBeenCalled();
    expect(console.error).not.toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
    expect(req.merchant).toEqual(expect.any(Object));
    expect(req.merchant.test).toBe(decryptedMerchant.test);
  });
});