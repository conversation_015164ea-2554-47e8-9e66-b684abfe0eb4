const createError = require('http-errors');
const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');

const csrf = require('csurf');
const csrfProtection = csrf({ 
  cookie: {
    secure: true,
    httpOnly: true
  }
});
const appInsights = require('applicationinsights');

appInsights.setup()
  .setAutoDependencyCorrelation(true)
  .setAutoCollectRequests(true)
  .setAutoCollectPerformance(true)
  .setAutoCollectExceptions(true)
  .setAutoCollectDependencies(true)
  .setAutoCollectConsole(true, true)
  .start();
appInsights.defaultClient.commonProperties = {
  environment: process.env.NODE_ENV,
};

const { router, setupRouter } = require('./routes/router');
const StatusMappingService = require('./services/statusMappingService');
const CountryISOService = require('./services/countryISO-service');
const HScodeMappingService = require('./services/hsCodeMappingService');
const keyvault_service = require('./services/keyvault-service');
const { requestLog } = require('./middlewares');
const keyvaultKeys = require('./const/keyvault');
const RedisClient = require('./utilities/redis-client');
require('dotenv').config();
const debug = require('debug')('server:server');
const http = require('http');
const { Server } = require('socket.io');
const { createAdapter } = require('@socket.io/redis-adapter');

function normalizePort(val) {
  const port = parseInt(val, 10);

  if (isNaN(port)) {
    // named pipe
    return val;
  }

  if (port >= 0) {
    // port number
    return port;
  }

  return false;
}

function onError(error) {
  if (error.syscall !== 'listen') {
    throw error;
  }

  const bind = typeof port === 'string'
    ? 'Pipe ' + port
    : 'Port ' + port;


  switch (error.code) {
  case 'EACCES':
    console.error(bind + ' requires elevated privileges');
    process.exit(1);
    break;
  case 'EADDRINUSE':
    console.error(bind + ' is already in use');
    process.exit(1);
    break;
  default:
    throw error;
  }
}

const port = normalizePort(process.env.PORT || '3001');

const app = express();

async function main() {
  app.disable('x-powered-by');
  app.use(requestLog);

  app.use(['/merchant/v2/uploadMerchant', '/merchant/notifications/create'], express.json({ limit: '2MB' }));
  app.use(express.json({
    verify: (req, res, buf) => {
      req.rawBody = buf;
    }
  }));
  app.use(express.urlencoded({ extended: false }));
  app.use(cookieParser());
  app.use(express.static(path.join(__dirname, 'public')));
  app.set('trust proxy', 1);

  app.use(function (req, res, next) {
    res.header('Access-Control-Allow-Methods', 'PUT');
    res.header('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept, authorization, x-xsrf-token');
    next();
  });

  const crsfRegex = /(?:\/\*!?|\*\/|[';]--|--[\r\n\v\f]|--[^-]*-|[^\\\-&]#.[\r\n\v\f]*|;?\\x00)/;

  app.get('/merchant/csrf-token', csrfProtection, function (req, res) {
    let retryCount = 0;
    let csrfToken = req.csrfToken();

    while (crsfRegex.test(csrfToken)) {
      retryCount++;
      console.log(`auth-merchant-portal CSRF ${csrfToken} not match security rule, ${retryCount} time retry`);

      csrfToken = req.csrfToken();
    }

    res.status(200).send({ 'XSRF-TOKEN': csrfToken });
  });

  app.put('*', csrfProtection);

  app.post('*', (req, res, next) => {
    if (req.url.toLowerCase() === '/merchant/order'
      || req.url.toLowerCase() === '/merchant/cancel'
      || req.url === '/merchant/parcel/download-labels-via-api'
      || req.url.toLowerCase() === '/merchant/calculate-potential-shipping-fee'
      || req.url.toLowerCase() === '/merchant/order/b2b'
      || req.url.toLowerCase() === '/merchant/shopify/order'
    ) {
      next();
    } else {
      csrfProtection(req, res, next);
    }
  });

  app.use('*', async (req, res, next) => { // Call middleware before any route
    await StatusMappingService.checkCache();
    await CountryISOService.checkCache();
    await HScodeMappingService.checkCache();
    return next();
  });

  app.use('/merchant', router);

  app.get('/', (req, res) => {
    res.status(200).json({ message: 'Merchant booking API' });
  });

  // catch 404 and forward to error handler
  app.use(function (req, res, next) {
    next(createError(404));
  });

  // error handler
  // eslint-disable-next-line no-unused-vars
  app.use(function (err, req, res, next) {
    // set locals, only providing error in development
    res.locals.message = err.message;
    res.locals.error = req.app.get('env') === 'development' ? err : {};

    // render the error page
    res.status(err.status || 500);
    res.json({
      message: err.message,
      error: err
    });
  });

  app.set('port', port);

  const settings = await keyvault_service.getKeyVaultSecret(keyvaultKeys); 
  
  const pubClient = RedisClient.init(keyvault_service.getValueByName('redis-key', settings));
  const subClient = pubClient?.duplicate();

  const server = http.createServer(app);
  const io = new Server(server, {
    adapter: createAdapter(pubClient, subClient),
    cors: {
      origin: 'http://localhost:3000'
    }
  });
  
  await setupRouter(settings, io);
  
  function onListening() {
    const addr = server.address();
    const bind = typeof addr === 'string'
      ? 'pipe ' + addr
      : 'port ' + addr.port;
    debug('Listening on ' + bind);
  }

  server.listen(port);
  server.on('error', onError);
  server.on('listening', onListening);
}

main().catch(err => console.log(err));
