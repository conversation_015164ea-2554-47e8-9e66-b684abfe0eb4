const { promisify } = require('util');
const redis = require('redis');

const RedisClient = {
  client: null,

  init: function (authKey, host = process.env.REDISCACHEHOSTNAME, port = 6380) {
    this.client = redis.createClient(port, host, { auth_pass: authKey, tls: { servername: host } });
    this.client.on('error', function (error) {
      console.log('redis-client init error', host, port, error);
    });

    return this.client;
  },

  getEnvCacheName(key) {
    return `[${process.env.APP_ENV}]_${key}`;
  },

  async setCacheStr(key, value, ttlInHrs) {
    const cacheName = this.getEnvCacheName(key);
    try {
      if (ttlInHrs) {
        const setAsync = promisify(this.client.setex).bind(this.client);
        await setAsync(cacheName, ttlInHrs * 3600, value);
      } else {
        const setAsync = promisify(this.client.set).bind(this.client);
        await setAsync(cacheName, value);
      }
    } catch (error) {
      console.log('redis-client setCache error', value, error);
    }
    return value;
  },

  async setCacheObj(key, value, ttlInHrs) {
    await this.setCacheStr(key, JSON.stringify(value), ttlInHrs);
    return value;
  },

  getCache: async function (name) {
    if (!this.client) {
      console.log('redis-client getCache client not initialized');
      return null;
    }

    try {
      const getAsync = promisify(this.client.get).bind(this.client);
      const valueStr = await getAsync(`[${process.env.APP_ENV}]_${name}`);
      return JSON.parse(valueStr);
    } catch (error) {
      console.log('redis-client getCache error', name, error);
      return null;
    }
  }
};

module.exports = RedisClient;
