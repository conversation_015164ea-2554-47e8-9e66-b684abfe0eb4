const config = require('../config');
const express = require('express');
const router = express.Router();
const auth = require('../auth/auth');
const auth2 = require('../auth/auth-merchant-portal');
const keyvault_service = require('../services/keyvault-service');
const { CosmosClient } = require('@azure/cosmos');
const { BlobServiceClient } = require('@azure/storage-blob');
const { QueueServiceClient } = require('@azure/storage-queue');

const ManifestItemsController = require('../controller/manifest_items_controller');
const BatchController = require('../controller/batch_controller');
const GenParcelLabelController = require('../controller/gen_parcel_label_controller');
const DestinationController = require('../controller/destination_controller');
const MerchantController = require('../controller/merchant_controller');
const BookingCancellationController = require('../controller/booking_cancellation_controller');
const BaseDao = require('../models/baseDao');
const AzureBlobStorage = require('../utilities/azure-blob-storage');
const AzureStorageQueue = require('../utilities/azure-queue-storage');
const rateMerchantServices = require('../services/rate-merchant-services');
const ManifestItemService = require('../services/manifest-item-services');
const ShipmentService = require('../services/shipment-services');
const OperationHubService = require('../services/operationHub.service');
const InvoiceController = require('../invoices/invoice.controller');
const InvoiceService = require('../invoices/invoice.service');
const ReportController = require('../report/report.controller');
const reportService = require('../report/report.service');
const MerchantService = require('../services/merchant-service');
const ShipmentController = require('../controller/shipment_controller');
const mawbService = require('../services/mawb-service');
const LmdService = require('../services/lmd-service');
const NotificationsService = require('../services/notification-service');
const NotificationController = require('../controller/notification_controller');
const BookingService = require('../services/booking/booking-service');
const CountryISOService = require('../services/countryISO-service');
const surchargeService = require('../services/surcharge-service');
const TaxRateService = require('../services/tax-rate-service');
const session = require('express-session');
const TrackingNoService = require('../services/tracking-no-services');
const authUtils = require('../utilities/authUtils');
const { onConnection, SocketService } = require('../socket/socket');
const ConfigService = require('../services/config-service');
const { ManagedIdentityCredential } = require('@azure/identity');

async function setupRouter(settings, io) {
  config.db.host = keyvault_service.getValueByName('host', settings);
  config.db.authKey = keyvault_service.getValueByName('authKey', settings);
  config.key.aesPassword = keyvault_service.getValueByName('aes-password', settings);
  config.key.aesSalt = keyvault_service.getValueByName('aes-salt', settings);
  config.key.aesCounter = Number(keyvault_service.getValueByName('aes-counter', settings));

  config.sendgrid.api_email = keyvault_service.getValueByName('sendgrid-email-from', settings);
  
  config.google_key.geocoding_api_key = keyvault_service.getValueByName('geocoding-api-key', settings);

  config.userSession.secretKey = keyvault_service.getValueByName('user-session-secret', settings);

  const client = new CosmosClient({ endpoint: config.db.host, key: config.db.authKey });

  const mawbDao = new BaseDao(client, config.db.databaseId, config.db.mawbColId);
  const manifestItemArchiveDao = new BaseDao(client, config.db.databaseId, config.db.manifestItemArchiveColId);
  const manifestItemDao = new BaseDao(client, config.db.databaseId, 
    config.db.manifestItemColId, manifestItemArchiveDao);
  const merchantDao = new BaseDao(client, config.db.databaseId, config.db.merchantColId);
  const operationHubDao = new BaseDao(client, config.db.databaseId, config.db.operationHub);
  const rateMerchantDao = new BaseDao(client, config.db.databaseId, config.db.rateMerchantColId);
  const gaylordDao = new BaseDao(client, config.db.databaseId, config.db.gaylord);
  const invoiceDao = new BaseDao(client, config.db.databaseId, config.db.invoice);
  const notificationDao = new BaseDao(client, config.db.databaseId, config.db.notification);
  const rejectedShipmentsDao = new BaseDao(client, config.db.databaseId, config.db.rejectedShipments);
  const surchargeDao = new BaseDao(client, config.db.databaseId, config.db.surcharge);
  const taxRateDao = new BaseDao(client, config.db.databaseId, config.db.taxRate);
  const counterDao = new BaseDao(client, config.db.databaseId, config.db.plsCounter);
  const configDao = new BaseDao(client, config.db.databaseId, config.db.configuration);

  const internalStorageAccount = keyvault_service.getValueByName('internal-storage-account', settings);
  const managedIdentityClientId = keyvault_service.getValueByName('managed-identity-client-id', settings);

  AzureBlobStorage.internalBlobService = new BlobServiceClient(`https://${internalStorageAccount}.blob.core.windows.net`, new ManagedIdentityCredential(managedIdentityClientId));
  AzureStorageQueue.internalQueueService = new QueueServiceClient(`https://${internalStorageAccount}.queue.core.windows.net`, new ManagedIdentityCredential(managedIdentityClientId));

  console.log('Start APIs');
  rateMerchantServices.rateMerchantDao = rateMerchantDao;
  rateMerchantServices.rateDao = rateMerchantDao;
  rateMerchantServices.operationHubDao = operationHubDao;
  reportService.manifestItemDao = manifestItemDao;
  reportService.gaylordDao = gaylordDao;
  reportService.mawbDao = mawbDao;
  mawbService.mawbDao = mawbDao;
  surchargeService.surchargeDao = surchargeDao;
  TaxRateService.taxRateDao = taxRateDao;
  InvoiceService.invoiceDao = invoiceDao;
  NotificationsService.notificationDao = notificationDao;
  BookingService.manifestItemDao = manifestItemDao;
  BookingService.rejectedShipmentsDao = rejectedShipmentsDao;

  authUtils.merchantDao = merchantDao;

  const manifestItemsController = new ManifestItemsController(manifestItemDao, 
    merchantDao, operationHubDao, gaylordDao);
  const batchController = new BatchController(manifestItemDao);
  const shipmentController = new ShipmentController(manifestItemDao);
  const genParcelLabelController = new GenParcelLabelController(manifestItemDao, merchantDao, operationHubDao);
  const merchantController = new MerchantController(merchantDao);
  const destinationController = new DestinationController();
  const bookingCancellationController = new BookingCancellationController(manifestItemDao);
  const merchantAuthen = new auth(merchantDao);
  const merchantAuthen2 = new auth2(merchantDao);

  const invoiceController = new InvoiceController();
  const reportController = new ReportController();
  const notificationController = new NotificationController();
  
  MerchantService.merchantDao = merchantDao;
  
  ManifestItemService.manifestItemDao = manifestItemDao;
  ManifestItemService.merchantDao = merchantDao;
  ConfigService.configDao = configDao;
  ShipmentService.manifestItemDao = manifestItemDao;
  ShipmentService.gaylordDao = gaylordDao;
  ShipmentService.mawbDao = mawbDao;
  OperationHubService.operationHubDao = operationHubDao;
  LmdService.manifestItemDao = manifestItemDao;
  TrackingNoService.counterDao = counterDao;

  router.get('/csrf-token', (req, res) => res.status(200).send({ message: 'Get CSRF token success' }));
  router.post('/Order',
    merchantAuthen.isAuthorized.bind(merchantAuthen),
    merchantController.makeSingleBooking.bind(merchantController)
  );
  router.post('/Order/b2b',
    merchantAuthen.isAuthorized.bind(merchantAuthen),
    merchantController.makeSingleBooking.bind(merchantController)
  );

  router.get('/locker-locations/:address?',
    merchantAuthen.isAuthorized.bind(merchantAuthen),
    destinationController.getLockerLocations.bind(destinationController)
  );

  router.post('/parcel/download-labels',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfoUI.bind(merchantAuthen2),
    genParcelLabelController.generateParcelLabels.bind(genParcelLabelController));

  router.post('/parcel/download-labels-via-api',
    merchantAuthen.isAuthorized.bind(merchantAuthen),
    genParcelLabelController.generateParcelLabels.bind(genParcelLabelController));

  router.post('/v2/uploadMerchant',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfoUI.bind(merchantAuthen2),
    merchantController.makeMultiBooking.bind(merchantController));

  router.post('/v2/book-splited-parcels',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfoUI.bind(merchantAuthen2),
    merchantController.bookSplitedParcels.bind(merchantController));

  router.post('/login',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    session({
      secret: config.userSession.secretKey || 'user-session-secret',
      cookie: { secure: true }, // Switch to false when working locally
      saveUninitialized: true,
      resave: false
    }),
    merchantController.login.bind(merchantController),
  );

  router.get('/parcel/:tracking_id',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    manifestItemsController.getParcelByTrackingID.bind(manifestItemsController));
  router.get('/batch/:batchNo',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    batchController.getBatchByBatchNo.bind(batchController));
  router.get('/batch/:batchNo/parcels',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    batchController.getBatchParcelsPaging.bind(batchController));

  router.post('/cancel',
    merchantAuthen.isAuthorized.bind(merchantAuthen),
    bookingCancellationController.cancelBooking.bind(bookingCancellationController)
  );

  router.get('/search',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    genParcelLabelController.getParcels.bind(genParcelLabelController));

  router.post('/cancelBooking',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfoUI.bind(merchantAuthen2),
    bookingCancellationController.cancelBooking.bind(bookingCancellationController));

  router.post('/logout',
    merchantAuthen2.validateToken.bind(merchantAuthen2), 
    (req, res) => res.status(200).send({ message: 'Logged out success' })
  );
  
  router.get('/getInvoicePaging',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    invoiceController.getInvoicePaging.bind(invoiceController)
  );

  router.post('/download-invoices',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    invoiceController.downloadInvoices.bind(invoiceController));

  router.post('/report', merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    reportController.downloadReport.bind(reportController));

  router.post('/reports',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    reportController.downloadReports.bind(reportController)
  );

  router.get('/report/csv-booking',
    reportController.generateCsvBookingReport.bind(reportController)
  );

  router.get('/check-report-available',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    reportController.checkReportAvailable.bind(reportController));

  router.get('/search-parcels', merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    manifestItemsController.searchParcels.bind(manifestItemsController));

  router.get('/getManifestItemPaging',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    shipmentController.getManifestItemPaging.bind(shipmentController)
  );

  router.get('/shopify/parcels',
    merchantAuthen.isAuthorized.bind(merchantAuthen),
    shipmentController.getManifestItemPaging.bind(shipmentController)
  );

  router.post('/shopify/order',
    merchantAuthen.isAuthorized.bind(merchantAuthen),
    merchantController.makeMultiBooking.bind(merchantController));

  router.get('/batch-shipments',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    batchController.getBatchShipments.bind(batchController)
  );

  router.get('/dashboard-data',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    manifestItemsController.getDashboardData.bind(manifestItemsController)
  );

  router.get('/notifications',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    notificationController.getNotifications.bind(notificationController)
  );

  router.post('/notifications/create',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfoUI.bind(merchantAuthen2),
    notificationController.addNewNotification.bind(notificationController)
  );

  router.post('/notifications',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfo.bind(merchantAuthen2),
    notificationController.updateNotification.bind(notificationController)
  );

  router.get('/notifications/:id/blob',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfoUI.bind(merchantAuthen2),
    notificationController.getNotificationBlob.bind(notificationController)
  );

  router.post('/generate-rejected-parcels',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfoUI.bind(merchantAuthen2),
    batchController.generateRejectedParcels.bind(batchController)
  );

  router.get('/downloadBookingTemplate',
    merchantAuthen2.validateToken.bind(merchantAuthen2),
    merchantAuthen2.injectMerchantInfoUI.bind(merchantAuthen2),
    shipmentController.getBookingTemplate.bind(shipmentController)
  );

  router.get('/countriesList', async (req, res) => {
    const result = await CountryISOService.getCountriesList();
    res.json(result);
  });

  router.get('/download-parcels-with-weight-changes',
    merchantAuthen.isAuthorized.bind(merchantAuthen),
    merchantController.downloadParcelsWithWeightChanges.bind(merchantController)
  );

  router.get('/pudo-points/:country?/:postalCode?',
    merchantAuthen.isAuthorized.bind(merchantAuthen),
    merchantController.getPudoPoints.bind(merchantController)
  );
  
  router.post('/calculate-potential-shipping-fee',
    merchantAuthen.isAuthorized.bind(merchantAuthen),
    merchantController.calculatePotentialShippingFee.bind(merchantController)
  );

  io
    .use(merchantAuthen2.validateTokenSocket.bind(merchantAuthen2))
    .use(merchantAuthen2.injectMerchantInfoSocket.bind(merchantAuthen2))
    .on('connection', onConnection);

  SocketService.socketIoInstance = io;
}

module.exports = { router, setupRouter } ;
