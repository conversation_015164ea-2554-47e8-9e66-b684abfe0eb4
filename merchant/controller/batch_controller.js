const StatusMappingService = require('../services/statusMappingService');
const ShipmentService = require('../services/shipment-services');
const { formatBatchNo } = require('../report/report.utils');
const { getParcelsBuffer } = require('../services/booking/booking-service');
const ManifestItemService = require('../services/manifest-item-services');
const { getValueOrDefault } = require('../utils');

function BatchController(manifestItemDao) {
  this.manifestItemDao = manifestItemDao;
}

module.exports = BatchController;

BatchController.prototype = {
  /**
   * Get batch info by batch number
   * @param req - request with path param batchNo
   * @param res
   * @return {Promise<object>}
   */
  getBatchByBatchNo: async function (req, res) {
    try {
      const batchNo = req.params.batchNo;
      const merchantName = req.merchant.merchant_name;

      // Validate batch ownership - ensure the batch belongs to the requesting merchant
      const isOwner = await ManifestItemService.validateBatchOwnership(merchantName, batchNo);
      if (!isOwner) {
        console.log('batch_controller getBatchByBatchNo - unauthorized access attempt', merchantName, batchNo);
        return res.status(404).json({
          isSuccess: false,
          message: 'Batch not found'
        });
      }

      const newBatchNo = formatBatchNo(batchNo);
      const createdDate = batchNo.split('-')[0];
      const batch = {
        new_batch_no: newBatchNo,
        date: createdDate
      };
      return res.json({
        isSuccess: true,
        data: batch
      });
    } catch (error) {
      console.log('batch_controller getBatchByBatchNo', error);
      return res.status(500).json({
        isSuccess: false,
        message: error.message
      });
    }
  },

  /**
   * Get all manifest items belong to one batch
   * @param req - request with query param batchNo
   * @param res
   * @return {Promise<object[]>}
   */
  getBatchParcelsPaging: async function (req, res) {

    try {
      const batchNo = req.params.batchNo;
      const {offset, limit} = req.query;
      const merchantName = req.merchant.merchant_name;

      const data = await ManifestItemService.getBatchParcelsPaging(
        merchantName,
        batchNo, 
        null, 
        null
      );


      const gaylordNos = data.data.map(item => item.gaylord_no).filter(item => item);
      let mawbs = [];
      if (gaylordNos?.length) {
        mawbs = await ShipmentService.getMAWBStatusByGaylordNos(gaylordNos);
      }

      data.data.forEach(item => {
        const parcelMawb = getValueOrDefault(mawbs[item.gaylord_no], []);
        const filteredNotShowStatus = item.tracking_status.filter(
          (stt) => StatusMappingService.isMerchantVisible(stt.status)
        );
        if (!StatusMappingService.isMerchantVisible(item.latest_tracking_status)) {
          item.latest_tracking_status = filteredNotShowStatus.at(-1).status;
        }
        
        const {blockchain, categoryStt} = ShipmentService.getMerchantLatestStatus(item, parcelMawb);
        item.merchantStatus = blockchain;
        item.categoryStt = categoryStt;
        item.cancellable = StatusMappingService.isCancellable(item.latest_tracking_status);
        item.schedulable = StatusMappingService.isSchedulable(item.latest_tracking_status);
      });

      data.data.forEach(parcel => {      
        if (parcel.parent_id === parcel.id) {
          parcel.children = [];
          for (const item of data.data) {
            if (item.parent_id &&  (item.parent_id !== item.id) && (item.parent_id === parcel.parent_id)) {      
              item.isCancel = true;      
              parcel.children.push(item);
              data.data = data.data.filter(element => element.id !== item.id);
            }
          }
        }
      });

      if (limit) {
        data.data = data.data.splice(offset, limit);
      }

      return res.status(200).json({
        isSuccess: true,
        ...data
      });
    } catch (error) {
      console.log('batch_controller getBatchParcelsPaging', error);
      return res.status(500).json({
        isSuccess: false,
        message: error.message
      });
    }
  },

  getBatchShipments: async function (req, res) {
    try {
      const { merchant_name } = req.merchant;
      const { page = 1, limit = 10 } = req.query;
      const data = await ManifestItemService.getBatchInfoByMerchant(merchant_name, page, limit);
      const { message } = data;
      data.message = message.map(item=>{
        item.new_batch_no = formatBatchNo(item.PLS_batch_no);
        return item;
      });
      res.json({
        success: true,
        ...data
      });
    } catch (err) {
      console.log('batch_controller getBatchShipments', err);
      res.json({
        success: false,
        message: err.message ? err.message : 'Something went wrong: Error occur when get batch shipments'
      });
    }
  },

  generateRejectedParcels: async function (req, res) {
    try {
      const { rejectedParcels } = req.body;
      if (!(rejectedParcels && rejectedParcels.length)) {
        res.json({
          success: false,
          message: 'Missing Rejected Parcels JSON'
        });
      }
      const { rejectedCSVBuffer } = getParcelsBuffer([], rejectedParcels);
      res.setHeader('Content-Type', 'text/csv');
      res.end(rejectedCSVBuffer);
    } catch (err) {
      console.log('batch_controller generateRejectedParcels', err);
      res.json({
        success: false,
        message: err.message ? err.message : 'Something went wrong: Error occur when get rejected parcels'
      });
    }
  }
};
