const { CrtCounterEncrypt, CrtCounterDecrypt } = require('../models/aesUtils');
const { bookingPlatform, parcelStatus, lmdProviders, DATE_FORMAT } = require('../const/enum');
const _ = require('lodash');
const StatusMappingService = require('./statusMappingService');
const logger = require('../utilities/logUtils');
const sortCode = require('../const/sortcodes');
const aesUtils = require('../models/aesUtils');
const { getTrackingIdHistory } = require('../utilities/parcelUtils');
const dateUtils = require('../utilities/dateUtils');
const AzureStorageQueue = require('../utilities/azure-queue-storage');
const { getBatchOrder } = require('../utilities/queryUtils');
const config = require('../config');

const ManifestItemService = {
  getParcelsByQuery: async function (querySpec) {
    return this.manifestItemDao.find(querySpec);
  },

  searchParcels: function (searchKeyWord, merchantName) {
    const querySpec = {
      query: `
        SELECT TOP 4 c.id, c.tracking_no, c.tracking_id, c.order_number, c.order_date 
        FROM c 
        WHERE c.merchant_name=@merchantName 
        AND ARRAY_CONTAINS([c.id, c.tracking_no, c.tracking_id, UPPER(c.order_number)], @searchText)
        OR EXISTS (SELECT VALUE n FROM n IN c.tracking_id_history WHERE n.tracking_id=@searchText)
        `,
      parameters: [
        {
          name: '@searchText',
          value: searchKeyWord.toUpperCase()
        },
        {
          name: '@merchantName',
          value: merchantName
        }
      ]
    };
    return this.manifestItemDao.find(querySpec);
  },

  findBookingsByIdsOrTrackingIds: async function (idsOrTrackingIds, merchantName, fields = '*') {
    if(Array.isArray(fields)){
      const text = fields.map(field => `c.${field}`).join(', ');
      fields = text;
    }
    const query = {
      query: `SELECT ${fields} FROM c WHERE c.merchant_name = @merchantName
                AND (ARRAY_CONTAINS(@ids, c.id) OR ARRAY_CONTAINS(@ids, c.tracking_id))
              ${getBatchOrder()}`,
      parameters: [
        {
          name: '@merchantName',
          value: CrtCounterEncrypt(merchantName)
        },
        {
          name: '@ids',
          value: idsOrTrackingIds
        }
      ]
    };
    const parcels = await this.manifestItemDao.find(query);
    return _.uniqBy(parcels, 'tracking_id');
  },

  getParcelById: async function (id) {
    try {
      const parcel = await this.manifestItemDao.getItem(id);

      if (parcel) {
        parcel.merchant_name = CrtCounterDecrypt(parcel.merchant_name);
      }

      return parcel;
    } catch (err) {
      console.log('manifest-item-services getParcelById', id, err);
    }

    return undefined;
  },

  findMP2ParcelsByBookingTime: async function (bookingTimeArr) {
    const query = {
      query: `SELECT * FROM c WHERE c.booking_platform = @bookingPlatform
        AND IS_DEFINED(c.csvFileName) AND ARRAY_CONTAINS(@bookingTimeArr, c.tracking_status[0].date)
        ${getBatchOrder()}`,
      parameters: [
        {
          name: '@bookingPlatform',
          value: bookingPlatform.newMerchantPortal
        },
        {
          name: '@bookingTimeArr',
          value: bookingTimeArr
        }
      ]
    };

    return this.manifestItemDao.find(query);
  },

  findSplitParcelsByBookingTime: async function (bookingTimeArr) {
    const query = {
      query: `SELECT * FROM c WHERE IS_DEFINED(c.parent_id)
        AND c.parent_id != c.id AND ARRAY_CONTAINS(@bookingTimeArr, c.tracking_status[0].date)
        ${getBatchOrder()}`,
      parameters: [
        {
          name: '@bookingTimeArr',
          value: bookingTimeArr
        }
      ]
    };

    return this.manifestItemDao.find(query);
  },

  /**
   * Get booking capacity utilisation in current week
   */
  getLmdReceivedBookings: async function (originCountry, fromDate, toDate, destinationGroups) {
    const query = {
      query: `
        SELECT c.destination_group
        FROM c
        WHERE (IS_DEFINED(c.tracking_id) OR c.latest_tracking_status = "Booked")
        AND IS_DEFINED(c.destination_group)
        AND IS_DEFINED(c.origin_country)
        AND c.origin_country = @originCountry
        AND c.tracking_status[0].date >= @startDate 
        AND c.tracking_status[0].date <= @endDate
        AND ARRAY_CONTAINS(@destinationGroups, c.destination_group)
        `,
      parameters: [
        {
          name: '@originCountry',
          value: originCountry
        },
        {
          name: '@startDate',
          value: fromDate
        },
        {
          name: '@endDate',
          value: toDate
        },
        {
          name: '@destinationGroups',
          value: destinationGroups
        }
      ]
    };
    const parcels = await this.manifestItemDao.find(query);

    return _.countBy(parcels, 'destination_group');
  },

  isProcessRejectedBooking: function (parcel) {
    return parcel.tracking_id === parcel.id && 
      parcel.tracking_status.every(tracking => 
        tracking.status !== StatusMappingService.getManifestStatus(parcelStatus.lmd_receive_booking));
  },

  /**
   * Get sort code from parcel.
   * <AUTHOR>
   * @param {*} parcel parcel to get sort code from.
   * @returns sort code get from parcel.
   * @since PLS-10977.
   */
  getSortCode(parcel) {
    //service type, incoterm, DG
    const { service_option: option, incoterm: term, destination_group: group } = parcel;
    return [sortCode.serviceOption[option], sortCode.incoterm[term], sortCode.destinationGroup[group]]
      .join('');
  },

  patch: async function (updatePayload) {
    await this.manifestItemDao.patchItem(updatePayload);
  },

  /**
     * Get all manifest items belong to one batch
     * @param merchantName merchant name where book the shipments
     * @param batchNo - Batch number
     * @param offset: number - Number of items to skip from beginner
     * @param limit: number - Maximum number of items to return
     * @return {Promise<{total: *, data: *}>}
     */
  getBatchParcelsPaging: async function (merchantName, batchNo, offset, limit) {
    // get parcels from a same batch by merchant from manifest_items

    const querySpec = {
      query: `SELECT
                  c.id,
                  c.tracking_id,
                  c.tracking_id_history,
                  c.service_option,
                  c.country,
                  c.order_number,
                  c.recipient_first_name,
                  c.recipient_last_name,
                  c.lmd,
                  c.PLS_batch_no,
                  c.latest_tracking_status,
                  c.tracking_status,
                  c.gaylord_no,
                  c.parent_id
              FROM c
              WHERE c.merchant_name = @merchantName
              AND c.PLS_batch_no = @batchNo
              ${getBatchOrder()}`,
      parameters: [
        {
          name: '@merchantName',
          value: merchantName
        },
        {
          name: '@batchNo',
          value: batchNo
        }
      ]
    };

    let parcels = await this.manifestItemDao.find(querySpec);
    const total = parcels.length;
    if (limit) {
      parcels = parcels.splice(offset, limit);
    }

    parcels.forEach(parcel => {
      parcel.country = aesUtils.CrtCounterDecrypt(parcel.country);
      parcel.recipient_first_name = aesUtils.CrtCounterDecrypt(parcel.recipient_first_name);
      parcel.recipient_last_name = aesUtils.CrtCounterDecrypt(parcel.recipient_last_name);
      parcel.tracking_id_history = getTrackingIdHistory(parcel);
    });

    return {
      data: parcels,
      total
    };
  },



  /**
   * Get batch shipments info by Merchant grouped by PLS batch number.
   * <AUTHOR>
   * @param merchantName merchant name where book the shipments
   * @param page The pagination page of displaying the shipments
   * @param limit The number of shipments per page
   * @returns shipment batch infor
   * @since PLS-12078.
   */
  getBatchInfoByMerchant: async function (merchantName, page, limit) {
    // Query items in 30 days
    const today = new Date();
    const priorDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 30);
    const dateRange = dateUtils.formatDateTime(priorDate, 'YYYY-MM-DD');

    const queryBatchShipments = {
      query: `
          SELECT c.PLS_batch_no, c.order_date 
          FROM c
          WHERE c.merchant_name = @merchantName 
          AND c.order_date > @dateRange
          ORDER BY c.merchant_name ASC, c.order_date DESC`,
      parameters: [
        {
          name: '@merchantName',
          value: merchantName
        },
        {
          name: '@dateRange',
          value: dateRange
        }
      ]
    };

    const shipmentsByMerchant = await this.manifestItemDao.find(queryBatchShipments);
    const offset = (page - 1) * limit;
    const batchShipments = _.transform(
      _.groupBy(shipmentsByMerchant, (t) => t.PLS_batch_no),
      (result, value) => result.push({ ...value[0], batch_size: value.length }),
      []
    ).slice(offset, offset + limit);

    if (!batchShipments.length) {
      throw new Error('You have no batchShipments');
    }
    return {
      total: batchShipments.length,
      message: batchShipments
    };
    
  },

  async triggerCat1MerchantBooking(parcel, triggerPoint = 'Booking'){
    try {
      if (!parcel.has_dims_weight) {
        logger.info(
          'triggerLmdBooking','Parcel missing dims weight, skip rebook parcel', parcel.id);

        return;
      }

      if (parcel.lmd === lmdProviders.NINJAVAN_SINGAPORE || parcel.has_hscode) {
        await AzureStorageQueue.sendBase64Message(config.azureQueueContainer.LMD_BOOKING_QUEUE, {
          parcelId: parcel.id,
          triggerPoint,
        });
        logger.info(
          'triggerLmdBooking','Trigger LMD booking for parcel', parcel.id);
      } else {
        logger.info(
          'triggerLmdBooking','Parcel missing HS code, skip rebook parcel', parcel.id);
      }
    } catch (error) {
      logger.error(
        'triggerLmdBooking', 
        'error while trigger lmd booking for parcel',
        `parcelId: ${parcel.id}`,
        `triggerPoint: ${triggerPoint}`,
        error,
      );
    }
  },

  // Generate array of last 6 months with their sort orders
  generateLastSixMonths: function() {
    const months = [];
    const today = new Date();
  
    for (let i = 0; i < 6; i++) {
      const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const fullFormat = dateUtils.formatDateTime(date, DATE_FORMAT.MONTH_YEAR);
      const month = fullFormat.slice(0, 3) + '\'' + fullFormat.slice(-2);
      const sortOrder = date.getFullYear() * 100 + (date.getMonth() + 1);
    
      months.push({ month, sortOrder });
    }
  
    return months;
  },

  // Create empty monthly data structure for all months
  initializeMonthlyData: function(months) {
    const monthlyData = {};
    months.forEach(monthItem => {
      monthlyData[monthItem.month] = { 
        month: monthItem.month,
        sortOrder: monthItem.sortOrder
      };
    });
    return monthlyData;
  },

  // Process a single item and update the monthly data
  processDataItem: function(item, monthlyData, countryCache) {
    if (!item.country || !item.order_date) {
      return;
    }
  
    // Get and cache decrypted country
    let country;
    if (countryCache.has(item.country)) {
      country = countryCache.get(item.country);
    } else {
      country = aesUtils.CrtCounterDecrypt(item.country);
      countryCache.set(item.country, country);
    }
  
    // Get month format in MMM'YY format
    const date = new Date(item.order_date);
    const fullFormat = dateUtils.formatDateTime(date, DATE_FORMAT.MONTH_YEAR);
    const month = fullFormat.slice(0, 3) + '\'' + fullFormat.slice(-2);
  
    // Skip if month is not in our 6-month range
    if (!monthlyData[month]) {
      return;
    }
  
    // Increment country count
    if (!monthlyData[month][country]) {
      monthlyData[month][country] = 1;
    } else {
      monthlyData[month][country]++;
    }
  },

  // Sort and clean the monthly data
  sortAndCleanMonthlyData: function(monthlyData) {
    let parcelVolume = Object.values(monthlyData);
    parcelVolume.sort((a, b) => a.sortOrder - b.sortOrder);
  
    return parcelVolume.map(item => {
      const newItem = { ...item };
      delete newItem.sortOrder;
      return newItem;
    });
  },

  queryMPDashboardData: async function (merchantName) {
    // Calculate date range for last 6 months
    const today = new Date();
    // Set to first day of 6 months ago
    const sixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 5, 1);
    // Set to last day of current month
    const endOfCurrentMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    const startDate = dateUtils.formatDateTime(sixMonthsAgo, DATE_FORMAT.YEAR_MONTH_DAY);
    const endDate = dateUtils.formatDateTime(endOfCurrentMonth, DATE_FORMAT.YEAR_MONTH_DAY);

    const query = {
      query: `
        SELECT 
          c.id,
          c.order_date,
          c.country,
          c.tracking_status
        FROM c
        WHERE c.merchant_name = @merchantName
        AND c.order_date >= @startDate
        AND c.order_date <= @endDate
      `,
      parameters: [
        {
          name: '@merchantName',
          value: merchantName
        },
        {
          name: '@startDate',
          value: startDate
        },
        {
          name: '@endDate',
          value: endDate
        }
      ]
    };

    return await this.manifestItemDao.find(query);
  },

  processDashboardData: async function (data) {
    // Generate last 6 months
    const months = this.generateLastSixMonths();
    
    // Handle undefined or empty data
    if (!data?.length) {
      return { 
        parcelVolume: months.map(monthItem => ({
          month: monthItem.month
        }))
      };
    }
    
    // Initialize data structures
    const countryCache = new Map();
    const monthlyData = this.initializeMonthlyData(months);
    
    // Process each item
    data.forEach(item => {
      this.processDataItem(item, monthlyData, countryCache);
    });
    
    // Sort and clean the data
    const parcelVolume = this.sortAndCleanMonthlyData(monthlyData);
    
    return { parcelVolume };
  },

  /**
   * Validate if a batch belongs to a specific merchant
   * @param merchantName merchant name to validate against
   * @param batchNo - Batch number to validate
   * @return {Promise<boolean>} true if batch belongs to merchant, false otherwise
   */
  validateBatchOwnership: async function (merchantName, batchNo) {
    try {
      const querySpec = {
        query: `SELECT VALUE COUNT(1) FROM c
                WHERE c.merchant_name = @merchantName
                AND c.PLS_batch_no = @batchNo`,
        parameters: [
          {
            name: '@merchantName',
            value: merchantName
          },
          {
            name: '@batchNo',
            value: batchNo
          }
        ]
      };

      const result = await this.manifestItemDao.find(querySpec);
      return result && result.length > 0 && result[0] > 0;
    } catch (err) {
      console.log('manifest-item-services validateBatchOwnership', merchantName, batchNo, err);
      return false;
    }
  },
};

module.exports = ManifestItemService;
