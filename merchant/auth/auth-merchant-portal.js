const aesUtils = require('../models/aesUtils');
const authUtils = require('../utilities/authUtils');
const merchantService = require('../services/merchant-service');
const userSessionService = require('../services/user-session-service');
const {throttledByIdentifierFunctionGenerator} = require('../utilities/throttle.utils');
const Jose = require('jose');
const axios = require('axios');
const { error } = require('../utilities/logUtils');

const logActivityTimeThrottled = throttledByIdentifierFunctionGenerator(
  authUtils.logActivityTime.bind(authUtils),
  60 * 1000
);

function MerchantAuthen2(merchantDao){
  this.merchantDao = merchantDao;
}

module.exports = MerchantAuthen2;

MerchantAuthen2.prototype = {
  injectMerchantInfo: async function (req, res, next) {
    const email = req.decoded.emails[0];
    const url = req.url;

    try {
      const merchants = await merchantService.getMerchantByAccountEmail(email);
      if (merchants && merchants.length > 0) {
        req.merchant = merchants[0];
        return next();
      }
    } catch (err) {
      console.error('auth-merchant-portal injectMerchantInfo exception', email, url, err);
    }

    console.log('auth-merchant-portal injectMerchantInfo 403 merchant not found', email, url);
    return res.status(403).json({ error: 'You have not permission to perform this action' });
  },

  /**
   * Inject decrypted merchant info, plan to replace injectMerchantInfo which is encrypted merchant
   */
  injectMerchantInfoUI: async function (req, res, next) {
    const loginEmail = req.decoded.emails[0];
    const url = req.url;

    try {
      const merchants = await merchantService.getMerchantByAccountEmail(loginEmail);
      if (merchants && merchants.length > 0) {
        req.merchant = aesUtils.decryptMerchant(merchants[0]);
        return next();
      }
    } catch (err) {
      console.error('auth-merchant-portal injectMerchantInfoUI exception', loginEmail, url, err);
    }

    console.log('auth-merchant-portal injectMerchantInfo 403 merchant not found', loginEmail, url);
    return res.status(403).json({ error: 'You have not permission to perform this action' });
  },
  injectMerchantInfoSocket: async function (socket, next) {
    try {
      const loginEmail = socket.decoded.emails[0];
      const merchants = await merchantService.getMerchantByAccountEmail(loginEmail);
      if (!merchants || !merchants.length) {
        throw new Error('You have not permission to perform this action');
      }

      socket.merchant = aesUtils.decryptMerchant(merchants[0]);
      return next();

    } catch (err) {
      error('MP Portal Authentication', 'Fail to inject merchant info socket', err);
      return next(err);
    }
  },
  validateToken: async function(req, res, next){
    try {
      // Validate B2C access token
      const jwt = req.headers.authorization.replace('Bearer', '').trim();
      const publicKey = await this.getPublicKey();
      const keyClaims = await this.verifyJWT(jwt, publicKey);
      if(!keyClaims) return res.status(401).send({ message: 'Unauthorized. The access token is invalid.' });
      
      req.decoded = keyClaims;

      // Validate user session
      await this.validateUserSession(req, res);
      
      // Update merchant activity
      logActivityTimeThrottled(keyClaims.emails[0], keyClaims.emails[0]);
      
      next();
    } catch (e) {
      error('MP Portal Authentication', 'Fail to validate token', e);
      return res.status(401).send({ message: `Unauthorized. ${e.message}` });
    }
  },
  validateTokenSocket: async function(socket, next){
    try {
      // Validate B2C access token
      const jwt = socket.handshake.auth.authorization.replace('Bearer', '').trim();
      const publicKey = await this.getPublicKey();
      const keyClaims = await this.verifyJWT(jwt, publicKey);
      if(!keyClaims) throw new Error('Unauthorized. The access token is invalid.');
      
      socket.decoded = keyClaims;
      
      return next();
    } catch (err) {
      error('MP Portal Socket Authentication', 'Fail to validate token', error);
      return next(err);
    }
  },
  validateUserSession: async function(req, res) {
    if(req.cookies['connect.sid']) {
      const sessionId = req.cookies['connect.sid'].split('.')[0].slice(2);
      const session = await userSessionService.get(req.decoded.sub);
      
      if(!session) {
        return res.clearCookie('connect.sid').status(401).send({
          message: 'The user session is not existed!'
        });
      }
      
      if(session.session_id !== sessionId) {
        return res.clearCookie('connect.sid').status(401).send({
          message: 'Your account has been logged in on another browser/device.'
        });
      }

      if(!req.url.includes('notifications')) {
        await userSessionService.set({
          user_id: session.id,
          session_id: session.session_id
        });
      }
    }    
  },
  verifyJWT: async function(jwt, publicKey) {
    try {
      const result = await Jose.jwtVerify(jwt, publicKey);
      return result.payload;
    } catch (e) {
      return false;
    }
  },
  getPublicKey: async function() {
    const { B2C_TENANT_NAME: name, B2C_POLICY_NAME: policy } = process.env;
    const url = `https://${name}.b2clogin.com/${name}.onmicrosoft.com/${policy}/discovery/v2.0/keys`;
    const jwkResponse = await axios.get(url);
    const jwk = jwkResponse.data.keys[0];
    const alg = 'RS256';
    return await Jose.importJWK(jwk, alg);
  },
};
