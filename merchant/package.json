{"name": "merchant-api", "version": "5.1.0", "description": "Booking API for merchants", "private": true, "scripts": {"start": "nodemon app.js", "local": "set NODE_TLS_REJECT_UNAUTHORIZED=0&& nodemon app.js", "debug": "set NODE_TLS_REJECT_UNAUTHORIZED=0&& nodemon --inspect app.js", "eslint": "eslint . --ignore-path ../.gitignore", "postinstall": "cd .. && husky install merchant/.husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --quiet"}, "overrides": {"jsonwebtoken": "^9.0.0"}, "dependencies": {"@azure/cosmos": "^3.17.3", "@azure/identity": "^4.2.1", "@azure/keyvault-secrets": "^4.0.2", "@azure/storage-blob": "^12.5.0", "@azure/storage-queue": "^12.15.0", "@socket.io/redis-adapter": "^8.3.0", "aes-js": "^3.1.2", "applicationinsights": "^1.8.10", "axios": "^1.7.4", "cookie-parser": "^1.4.5", "csurf": "^1.2.2", "csvtojson": "^2.0.10", "date-fns": "^2.30.0", "dotenv": "^6.2.0", "express": "^4.18.2", "express-session": "^1.17.3", "formidable": "^3.2.5 ", "http-errors": "~1.6.2", "husky": "^8.0.1", "jose": "^4.14.4", "json2csv": "^4.5.4", "jszip": "^3.7.1", "lodash": "^4.17.21", "nodemon": "^3.0.2", "pbkdf2": "^3.1.2", "pdf-lib": "^1.17.1", "redis": "^3.1.2", "socket.io": "^4.7.5", "tmp": "^0.1.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz"}, "devDependencies": {"eslint": "^8.27.0", "lint-staged": "^13.0.3"}}