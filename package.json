{"name": "fin-api", "version": "5.0.0", "description": "financial API", "main": "index.js", "author": "SIA", "license": "ISC", "private": true, "scripts": {"zip": "data_version=`node -p \"require('./package.json').version\"` && mkdir -p ./artifact && cd dist && zip -r ../artifact/fin-api-$data_version.zip .", "dev": "ENV=dev npm run build:live", "debug": "NODE_DEBUG=http npm run build:live", "build": "npm run clean && tsc --p ./tsconfig.build.json && echo 'build success!'", "build:live": "nodemon --watch 'src/**/*.ts' --exec 'ts-node' src/main/server.ts", "start": "NODE_ENV=production ENV=dev node ./dist/server.js", "clean": "rm -fr coverage dist", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "postinstall": "husky install", "lint": "eslint src --ignore-path .gitignore", "lint:quiet": "npm run lint -- --quiet", "lint:fix": "npm run lint -- --fix --quiet"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --quiet"}, "dependencies": {"@azure/cosmos": "^3.17.3", "@azure/identity": "^4.2.1", "@azure/keyvault-secrets": "^4.0.2", "@azure/storage-blob": "^12.1.2", "@azure/storage-queue": "^12.14.0", "@hapi/hapi": "^20.2.2", "aes-js": "^3.1.2", "applicationinsights": "^1.7.6", "axios": "^1.7.4", "bwip-js": "^2.0.10", "csv-parser": "^3.0.0", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "fast-xml-parser": "^4.2.4", "husky": "^8.0.0", "jszip": "^3.7.1", "lodash": "^4.17.20", "pbkdf2": "^3.1.1", "pdf-lib": "^1.17.1", "redis": "^3.1.2", "ssh2-sftp-client": "^9.0.4", "typed-rest-client": "^1.7.3", "uuid": "^9.0.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz"}, "devDependencies": {"@types/aes-js": "^3.1.1", "@types/bwip-js": "^1.7.0", "@types/eslint": "^8.56.0", "@types/jest": "^27.4.0", "@types/lodash": "^4.14.162", "@types/node": "^11.15.27", "@types/pbkdf2": "^3.0.0", "@types/redis": "^2.8.31", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-unicorn": "^49.0.0", "jest": "^29.0.3", "lint-staged": "^12.5.0", "mockdate": "^3.0.2", "nodemon": "^3.1.4", "ts-jest": "^29.0.2", "ts-node": "^8.1.0", "typescript": "^4.1.3"}, "overrides": {"jsonwebtoken": "^9.0.0", "tough-cookie": "^4.0.0"}}