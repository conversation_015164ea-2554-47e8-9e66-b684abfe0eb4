process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

const { CosmosClient } = require("@azure/cosmos");
const fs = require("fs");
const https = require("https");

const endpoint = "https://localhost:8081";
const key =
  "C2y6yDjf5/R+ob0N8A7Cgv30VRDJIWEHLM+4QDU5DE2nQ9nDuVTqobD4b8mGGyPMbIZnqyMsEcaGQy67XIw/Jw==";

const databaseId = "plsdb";
const containerId = "manifest_items";
const partitionKeyPath = undefined; // ⚠️ Không có partition key

const client = new CosmosClient({
  endpoint,
  key,
  allowInsecureConnection: true,
  agent: false,
});

async function createContainerIfNotExists() {
  const { database } = await client.databases.createIfNotExists({
    id: databaseId,
  });

  const containerDef = {
    id: containerId,
  };

  if (partitionKeyPath) {
    containerDef.partitionKey = {
      paths: [partitionKeyPath],
    };
  }

  const { container } = await database.containers.createIfNotExists(
    containerDef
  );

  console.log(`✅ Container '${containerId}' ready.`);
  return container;
}

async function restoreUsers(container) {
  const fileContent = fs.readFileSync("./backup/manifest_items.json", "utf8");
  const users = JSON.parse(fileContent);

  for (const user of users) {
    try {
      await container.items.create(user);
      console.log(`✅ Restored user: ${user.id}`);
    } catch (err) {
      console.error(`❌ Failed to restore user: ${user.id}`, err.message);
    }
  }

  console.log("🎉 Restore completed.");
}

async function main() {
  const container = await createContainerIfNotExists();
  await restoreUsers(container);
}

main().catch((err) => {
  console.error("🔥 Error:", err);
});
